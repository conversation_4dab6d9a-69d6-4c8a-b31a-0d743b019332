package com.qmqb.imp.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.system.domain.Bug;
import com.qmqb.imp.system.domain.ZtLang;
import com.qmqb.imp.system.domain.enums.BugSourceTypeEnum;
import com.qmqb.imp.system.domain.enums.EnvironmentEnum;
import com.qmqb.imp.system.domain.process.ProcessFinancialDataModification;
import com.qmqb.imp.system.domain.process.ProcessSystemAlarm;
import com.qmqb.imp.system.domain.vo.BugPageVO;
import com.qmqb.imp.system.domain.vo.process.ProcessFinancialDataModificationVo;
import com.qmqb.imp.system.domain.vo.process.ProcessSystemAlarmVo;
import com.qmqb.imp.system.mapper.BugMapper;
import com.qmqb.imp.system.mapper.ZtBugMapper;
import com.qmqb.imp.system.mapper.ZtLangMapper;
import com.qmqb.imp.system.mapper.process.ProcessFinancialDataModificationMapper;
import com.qmqb.imp.system.mapper.process.ProcessSystemAlarmMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 同步bug
 * @date 2025/3/4 15:50
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncBugService {

    private final ProcessSystemAlarmMapper processSystemAlarmMapper;
    private final ProcessFinancialDataModificationMapper processFinancialDataModificationMapper;
    private final ZtBugMapper ztBugMapper;
    private final BugMapper bugMapper;
    private final ZtLangMapper ztLangMapper;

    /**
     * 主干版本号
     */
    public static final String TRUNK_VERSION = "trunk";

    /**
     * 废弃bug类型
     */
    public static final String ABANDONED_TYPE = "abandoned";

    /**
     * 同步bug定时任务
     */
    @TraceId("同步bug")
    @XxlJob("syncBugJobHandler")
    public ReturnT<String> syncBug(String param) {
        try {
            XxlJobLogger.log("开始执行同步bug定时任务...");
            log.info("开始执行同步bug定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();
            Map<String, String> typeNameMap = ztLangMapper.selectList(new LambdaQueryWrapper<ZtLang>()
                    .eq(ZtLang::getModule, "bug")
                    .eq(ZtLang::getSection, "typeList")
                    .eq(ZtLang::getLang, "zh-cn")).stream()
                .filter(lang -> !StringUtils.equalsAny(lang.getKey(), ABANDONED_TYPE, ""))
                .collect(Collectors.toMap(ZtLang::getValue, ZtLang::getKey));
            syncProcessSystemAlarm(typeNameMap);
            syncProcessFinancialDataModification(typeNameMap);
            syncZtBug(typeNameMap);
            sw.stop();
            XxlJobLogger.log("同步bug定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("同步bug定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("同步bug定时任务执行异常: {}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }

    private void syncProcessSystemAlarm(Map<String, String> typeNameMap) {
        Long minId = -1L;
        List<ProcessSystemAlarmVo> processSystemAlarmVoList = processSystemAlarmMapper.selectVoList(new LambdaQueryWrapper<ProcessSystemAlarm>()
            .gt(ProcessSystemAlarm::getId, minId).orderByAsc(ProcessSystemAlarm::getId).last("limit 1000"));
        while (CollectionUtils.isNotEmpty(processSystemAlarmVoList)) {
            List<Bug> list = processSystemAlarmVoList.stream().map(data -> {
                Bug bug = new Bug();
                bug.setEnv(EnvironmentEnum.PROD.getCode());
                bug.setTitle(data.getFaultTitle());
                bug.setType(typeNameMap.getOrDefault(data.getBugType(), ABANDONED_TYPE));
                bug.setTypeName(typeNameMap.containsKey(data.getBugType()) ? data.getBugType() : ABANDONED_TYPE);
                bug.setProductName(data.getProductName());
                bug.setProjectName(data.getProjectName());
                bug.setSourceType(BugSourceTypeEnum.TB_PROCESS_SYSTEM_ALARM.getCode());
                bug.setSourceId(data.getId());
                bug.setOpenedByName(data.getOriginatorUser());
                bug.setOpenedDate(DateTimeUtil.of(data.getOriginatorTime()));
                return bug;
            }).collect(Collectors.toList());
            bugMapper.batchInsertOnUpdate(list);
            minId = list.stream().map(Bug::getId).max(Long::compareTo).get();
            processSystemAlarmVoList = processSystemAlarmMapper.selectVoList(new LambdaQueryWrapper<ProcessSystemAlarm>()
                .gt(ProcessSystemAlarm::getId, minId).orderByAsc(ProcessSystemAlarm::getId).last("limit 1000"));
        }
    }

    private void syncProcessFinancialDataModification(Map<String, String> typeNameMap) {
        Long minId = -1L;
        List<ProcessFinancialDataModificationVo> processFinancialDataModificationVoList = processFinancialDataModificationMapper
            .selectVoList(new LambdaQueryWrapper<ProcessFinancialDataModification>().gt(ProcessFinancialDataModification::getId, minId)
                .orderByAsc(ProcessFinancialDataModification::getId).last("limit 1000"));
        while (CollectionUtils.isNotEmpty(processFinancialDataModificationVoList)) {
            List<Bug> list = processFinancialDataModificationVoList.stream().map(data -> {
                Bug bug = new Bug();
                bug.setEnv(EnvironmentEnum.PROD.getCode());
                bug.setTitle(data.getTitle());
                bug.setType(typeNameMap.getOrDefault(data.getBugType(), ABANDONED_TYPE));
                bug.setTypeName(typeNameMap.containsKey(data.getBugType()) ? data.getBugType() : ABANDONED_TYPE);
                bug.setProductName(data.getProductName());
                bug.setProjectName(data.getProjectName());
                bug.setSourceType(BugSourceTypeEnum.TB_PROCESS_FINANCIAL_DATA_MODIFICATION.getCode());
                bug.setSourceId(data.getId());
                bug.setOpenedByName(data.getOriginatorUser());
                bug.setOpenedDate(DateTimeUtil.of(data.getOriginatorTime()));
                return bug;
            }).collect(Collectors.toList());
            bugMapper.batchInsertOnUpdate(list);
            minId = list.stream().map(Bug::getId).max(Long::compareTo).get();
            processFinancialDataModificationVoList = processFinancialDataModificationMapper.selectVoList(new LambdaQueryWrapper<ProcessFinancialDataModification>()
                .gt(ProcessFinancialDataModification::getId, minId).orderByAsc(ProcessFinancialDataModification::getId).last("limit 1000"));
        }
    }

    private void syncZtBug(Map<String, String> typeNameMap) {
        Long minId = -1L;
        List<BugPageVO> processSystemAlarmVoList = ztBugMapper.syncInfoList(minId);
        while (CollectionUtils.isNotEmpty(processSystemAlarmVoList)) {
            List<Bug> list = processSystemAlarmVoList.stream().map(data -> {
                Bug bug = new Bug();
                BeanUtils.copyProperties(data, bug);
                if (TRUNK_VERSION.equals(bug.getOpenedBuild())) {
                    bug.setOpenedBuildName("主干");
                }
                bug.setType(typeNameMap.getOrDefault(data.getTypeName(), ABANDONED_TYPE));
                bug.setTypeName(typeNameMap.containsKey(data.getTypeName()) ? data.getTypeName() : ABANDONED_TYPE);
                bug.setEnv(EnvironmentEnum.TEST.getCode());
                bug.setSourceType(BugSourceTypeEnum.ZT_BUG.getCode());
                bug.setSourceId(data.getId());
                return bug;
            }).collect(Collectors.toList());
            bugMapper.batchInsertOnUpdate(list);
            minId = processSystemAlarmVoList.stream().map(BugPageVO::getId).max(Long::compareTo).get();
            processSystemAlarmVoList = ztBugMapper.syncInfoList(minId);
        }
    }

}
