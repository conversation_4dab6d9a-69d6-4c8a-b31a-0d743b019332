package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiAttendanceGetcolumnvalResponse;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.UserLeaveTypeEnum;
import com.qmqb.imp.common.enums.WarnCodeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.api.client.DingTalkOApiClient;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.bo.WarnContentParamsBo;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.mapper.UserKqStatMapper;
import com.qmqb.imp.system.mapper.UserLeaveMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.warn.ISendWarnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 钉钉考勤数据同步
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceService {

    private final DingTalkOApiClient dingTalkOApiClient;
    private final IUserKqStatService userKqStatService;
    private final UserKqStatMapper userKqStatMapper;
    private final SysUserMapper sysUserMapper;
    private final ISendWarnService sendWarnService;
    private final UserLeaveMapper userLeaveMapper;

    /*** 旷工天数*/
    private static final long ABSENTEEISM_DAYS = 124818985L;
    /*** 出勤天数*/
    private static final long ATTENDANCE_DAYS = 124818973L;
    /*** 工作时长*/
    private static final long ATTENDANCE_WORK_TIME = 124818975L;
    /*** 迟到时长*/
    private static final long LATE_MINUTE = 124818977L;
    /*** 加班-审批单统计*/
    private static final long OVERTIME_APPROVE_COUNT = 124818989L;
    /*** 迟到次数*/
    private static final long LATE_COUNT = 124818976L;
    /*** 严重迟到时长*/
    private static final long YZ_LATE_TIMES = 124818979L;
    /*** 严重迟到次数*/
    private static final long YZ_LATE_COUNT = 124818978L;
    /*** 早退次数*/
    private static final long LEAVE_EARLY_COUNT = 124818981L;
    /*** 表单列id*/
    private static final String COLUMN_ID_LIST = "124818985,124818973,124818975,124818977,124818989,124818976,124818979,124818978,124818981";


    /**
     * 同步考勤数据定时任务
     */
    @TraceId("同步考勤")
    @XxlJob("syncAttendanceJobHandler")
    public ReturnT<String> syncAttendanceJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行同步考勤定时任务...");
            log.info("开始执行同步考勤定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();
            Date now = new Date();
            if (StringUtils.isNotBlank(param)) {
                now = DateUtil.parseDate(param);
            }
            // 查询本月数据
            DateTime fromDate = DateUtil.beginOfMonth(now);
            DateTime toDate = DateUtil.endOfMonth(now);
            List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.lambdaQuery(SysUser.class).notIn(SysUser::getUserId, UserConstants.ADMIN_ID, UserConstants.JSZX_ADMIN_ID));
            String kqYear = String.valueOf(DateUtil.year(now));
            String kqMonth = String.valueOf(DateUtil.month(now) + 1);
            List<UserKqStat> data = new ArrayList<>();
            sysUsers.forEach(user -> {
                UserKqStat userKqStat = buildUserKpStat(user, kqYear, kqMonth, fromDate, toDate);
                data.add(userKqStat);
            });
            if (CollUtil.isNotEmpty(data)) {
                userKqStatMapper.insertOrUpdateBatch(data);
            }
            sw.stop();
            XxlJobLogger.log("同步考勤定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("同步考勤定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("同步考勤定时任务执行异常", e);
            long batchNo = IdUtil.getId();
            try {
                log.info("发送同步考勤预警批次号:{}", batchNo);
                sendWarnService.sendWarn(batchNo, WarnCodeEnum.SYN_DD_WARN_P0.getCode(),
                    Collections.singletonList(WarnContentParamsBo.builder().paramsCode("date").paramsValue(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD)).build()), null);
            }catch (Exception exception) {
                log.error("发送同步考勤预警异常, 批次号:{}", batchNo, exception);
            }
            return ReturnT.FAIL;
        }
    }

    public UserKqStat buildUserKpStat(SysUser user, String kqYear, String kqMonth,DateTime fromDate,DateTime toDate) {
        log.debug("开始处理{}的考勤统计", user.getUserName());
        String userName = user.getNickName();
        UserKqStat stat = userKqStatService.queryByUserNameAndKqYearAndKqMonth(userName, kqYear, kqMonth);
        if (Objects.isNull(stat)) {
            stat = new UserKqStat();
            stat.setKqUserName(userName);
            stat.setKqYear(kqYear);
            stat.setKqMonth(kqMonth);
        }
        String dingtalkUserId = user.getDingtalkUserId();
        List<OapiAttendanceGetcolumnvalResponse.ColumnValForTopVo> vos = dingTalkOApiClient.getAttendanceColumnsValue(fromDate, toDate, dingtalkUserId, COLUMN_ID_LIST);
        for (OapiAttendanceGetcolumnvalResponse.ColumnValForTopVo vo : vos) {
            OapiAttendanceGetcolumnvalResponse.ColumnForTopVo columnVo = vo.getColumnVo();
            List<OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal> columnVals = vo.getColumnVals();
            BigDecimal sum = columnVals.stream().map(e -> StringUtils.isNotBlank(e.getValue()) ? new BigDecimal(e.getValue()) : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (Objects.equals(columnVo.getId(), ABSENTEEISM_DAYS)) {
                stat.setKqAbsenteeismDays(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), ATTENDANCE_DAYS)) {
                BigDecimal attendance = columnVals.stream().filter(e -> !isWorkDay(e.getDate()))
                    .map(e -> {
                        if (StringUtils.isNotBlank(e.getValue())) {
                            //0.93:请假半小时也算出勤
                            return new BigDecimal(e.getValue()).compareTo(new BigDecimal("0.93")) >= 0 ? BigDecimal.ONE : BigDecimal.ZERO;
                        }
                        return BigDecimal.ZERO;
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
                Long leaveCount = caculateLeaveDays(kqYear, kqMonth, userName);
                stat.setKqAttendanceDays(attendance.longValue() - leaveCount);
            } else if (Objects.equals(columnVo.getId(), ATTENDANCE_WORK_TIME)) {
                stat.setKqAttendanceWorkTime(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), LATE_MINUTE)) {
                stat.setKqLateMinute(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), OVERTIME_APPROVE_COUNT)) {
                stat.setKqOvertimeApproveCount(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), LATE_COUNT)) {
                stat.setKqLateCount(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), YZ_LATE_TIMES)) {
                stat.setKqYzLateTimes(sum.longValue());
            } else if (Objects.equals(columnVo.getId(), YZ_LATE_COUNT)) {
                stat.setKqYzLateCount(sum.longValue());
            }else if (Objects.equals(columnVo.getId(), LEAVE_EARLY_COUNT)) {
                stat.setKqLeaveEarlyCount(sum.longValue());
            }
        }
        return stat;
    }

    /**
     * 计算请假天数
     * @param year
     * @param month
     * @return
     */
    private Long caculateLeaveDays(String year, String month, String userName) {
        YearMonth targetMonth = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        LocalDate firstDayOfMonth = targetMonth.atDay(1);
        LocalDate lastDayOfMonth = targetMonth.atEndOfMonth();
        Date startDate = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(lastDayOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        List<UserLeaveVo> userLeaveList = userLeaveMapper.getUserLeaveList(
            startDate, endDate, userName, UserLeaveTypeEnum.COMPENSATORY_LEAVE.getCode());

        Set<LocalDate> effectiveLeaveDates = new HashSet<>();
        LocalDate currentDate = LocalDate.now();

        for (UserLeaveVo leave : userLeaveList) {
            LocalDateTime start = leave.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime end = leave.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
            // 处理跨天情况，遍历请假期间的每一天
            LocalDate date = start.toLocalDate();
            LocalDate lastDate = end.toLocalDate();
            while (!date.isAfter(lastDate)) {
                // 跳过未来日期
                if (date.isAfter(currentDate)) {
                    date = date.plusDays(1);
                    continue;
                }
                // 跳过非本月的日期
                if (!YearMonth.from(date).equals(targetMonth)) {
                    date = date.plusDays(1);
                    continue;
                }
                String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                // 检查是否是有效工作日
                if (HolidayUtil.isValidWorkDay(dateStr)) {
                    // 检查当天是否有有效请假时间（9:00-18:00之间）
                    if (hasEffectiveLeaveOnDay(date, start, end)) {
                        effectiveLeaveDates.add(date);
                    }
                }
                date = date.plusDays(1);
            }
        }

        return (long) effectiveLeaveDates.size();
    }
    /**
     * 判断当天是否有有效请假时间段
     */
    private boolean hasEffectiveLeaveOnDay(LocalDate date, LocalDateTime start, LocalDateTime end) {
        // 1. 定义工作时间段和午休时间段
        LocalDateTime workStart = date.atTime(9, 0);
        LocalDateTime noonStart = date.atTime(12, 0);
        LocalDateTime noonEnd = date.atTime(14, 0);
        LocalDateTime workEnd = date.atTime(18, 0);

        // 2. 调整请假时间到工作时间内
        LocalDateTime effectiveStart = start.isBefore(workStart) ? workStart :
            (start.isAfter(workEnd) ? workEnd : start);
        LocalDateTime effectiveEnd = end.isBefore(workStart) ? workStart :
            (end.isAfter(workEnd) ? workEnd : end);

        // 3. 如果请假完全在午休时间(12:00-14:00)，直接返回false
        boolean invalid = (effectiveStart.isAfter(noonStart) || effectiveStart.equals(noonStart)) && effectiveEnd.isBefore(noonEnd);
        if (invalid) {
            return false;
        }

        // 4. 计算有效请假时间（需要扣除午休时间）
        long leaveMinutes = 0;
        // 上午工作时间段(9:00-12:00)的请假
        if (effectiveStart.isBefore(noonStart)) {
            LocalDateTime endBeforeNoon = effectiveEnd.isBefore(noonStart) ? effectiveEnd : noonStart;
            leaveMinutes += Duration.between(effectiveStart, endBeforeNoon).toMinutes();
        }
        // 下午工作时间段(14:00-18:00)的请假
        if (effectiveEnd.isAfter(noonEnd)) {
            LocalDateTime startAfterNoon = effectiveStart.isAfter(noonEnd) ? effectiveStart : noonEnd;
            leaveMinutes += Duration.between(startAfterNoon, effectiveEnd).toMinutes();
        }
        // 6. 最终判断：有效请假时间>30分钟才算有效请假
        return leaveMinutes > 30;
    }


    /**
     * 判断时间戳是否是工作日
     *
     * @param date 时间
     * @return false 工作日
     */
    private boolean isWorkDay(Date date) {
        String strTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date);
        return HolidayUtil.isHoliday(strTime);
    }

}
