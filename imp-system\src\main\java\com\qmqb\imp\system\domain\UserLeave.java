package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 请假记录对象 tb_user_leave
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_user_leave")
public class UserLeave extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String nickName;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 请假开始时间
     */
    private Date startTime;
    /**
     * 请假结束时间
     */
    private Date endTime;
    /**
     * 请假持续时间
     */
    private String durationTime;
    /**
     * 请假类型
     */
    private String leaveType;
    /**
     * 审批人
     */
    private String leader;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 通过时间时间
     */
    private Date passTime;
    /**
     * 状态 0：默认 1：审批通过 2：撤销
     */
    private Integer status;
    /**
     * 钉钉单号
     */
    private String dingTalkNo;

}
