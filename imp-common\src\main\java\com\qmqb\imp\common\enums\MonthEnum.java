package com.qmqb.imp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 月份枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MonthEnum {

    JANUARY("01", 1, "一月"),
    FEBRUARY("02", 2, "二月"),
    MARCH("03", 3, "三月"),
    APRIL("04", 4, "四月"),
    MAY("05", 5, "五月"),
    JUNE("06", 6, "六月"),
    JULY("07", 7, "七月"),
    AUGUST("08", 8, "八月"),
    SEPTEMBER("09", 9, "九月"),
    OCTOBER("10", 10, "十月"),
    NOVEMBER("11", 11, "十一月"),
    DECEMBER("12", 12, "十二月"),
    ;
    private final String name;
    private final Integer value;
    private final String desc;

}
