<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.UserLeaveMapper">

    <resultMap type="com.qmqb.imp.system.domain.UserLeave" id="UserLeaveResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="durationTime" column="duration_time"/>
        <result property="leader" column="leader"/>
        <result property="applyTime" column="apply_time"/>
        <result property="passTime" column="pass_time"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="dingTalkNo" column="ding_talk_no"/>
    </resultMap>

    <select id="countByUserIdAndTime" resultType="java.lang.Integer">
        select count(*) from tb_user_leave where user_id=#{userId} and
                   ((start_time between #{startDateStr} and #{endDateStr}) or (end_time between #{startDateStr} and #{endDateStr}))
    </select>

    <select id="countByUserIdInRangeTime" resultType="java.lang.Integer">
        select count(*)
        from tb_user_leave
        where user_id = #{userId}
          AND ((start_time <![CDATA[ <= ]]> #{startDateStr}
            AND #{startDateStr} <![CDATA[ <= ]]>  end_time)
            or (start_time <![CDATA[ <= ]]> #{endDateStr}
                AND #{endDateStr} <![CDATA[ <= ]]> end_time))
    </select>

    <select id="statisticUserLeaveCount" resultType="com.qmqb.imp.system.domain.vo.UserLeaveVo">
        select  dept_name,dept_id,start_time,end_time,nick_name,user_id
        from tb_user_leave
        where dept_id = #{deptId}
          and (
            ( Year(start_time) = #{year} and Month(start_time) = #{month}) or
            ( Year(end_time) = #{year} and Month(end_time) = #{month})
            )
          and status = '1'
    </select>

    <select id="statisticUserLeave" resultType="com.qmqb.imp.system.domain.vo.UserLeaveVo">
        select  id, user_id, nick_name, dept_id, dept_name, start_time, end_time, duration_time, leader, apply_time, pass_time, status, create_by, create_time, update_by, update_time, ding_talk_no
        from tb_user_leave
        where
            dept_id in
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
              #{id}
            </foreach>
          and (
        (start_time &gt;= #{startTime} and start_time &lt;= #{endTime}) or
        (end_time &gt; #{startTime} and end_time &lt;= #{endTime}) or
        (start_time &lt;= #{startTime} and end_time &gt;= #{endTime})
        )
          and status = '1'
    </select>

    <select id="selectUserIdByDingtalk" resultType="java.lang.Long">
        SELECT user_id FROM sys_user WHERE dingtalk_user_id = #{id} AND del_flag = '0' LIMIT 1
    </select>

    <select id="judgeVacation" resultType="java.lang.Boolean">
        SELECT count(1)
        FROM tb_user_leave
        WHERE user_id = #{userId}
          AND DATE(start_time) <![CDATA[ <= ]]> DATE(#{date})
          AND DATE(#{date}) <![CDATA[ <= ]]>  DATE(end_time)
    </select>

    <insert id="batchInsertOnUpdate" parameterType="com.qmqb.imp.system.domain.UserLeave" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO tb_user_leave(id, user_id, nick_name, dept_id, dept_name, start_time, end_time, leader, apply_time, pass_time, status, ding_talk_no, duration_time, leave_type)VALUES
        <foreach collection="list" item="it" separator=",">
        (#{it.id}, #{it.userId}, #{it.nickName}, #{it.deptId}, #{it.deptName}, #{it.startTime}, #{it.endTime}, #{it.leader}, #{it.applyTime}, #{it.passTime}, #{it.status}, #{it.dingTalkNo}, #{it.durationTime}, #{it.leaveType})
        </foreach>
        ON DUPLICATE KEY UPDATE nick_name = VALUES(nick_name), dept_id = VALUES(dept_id), dept_name = VALUES(dept_name), start_time = VALUES(start_time), end_time = VALUES(end_time), leader = VALUES(leader), apply_time = VALUES(apply_time), pass_time = VALUES(pass_time), status = VALUES(status), ding_talk_no = VALUES(ding_talk_no), duration_time = VALUES(duration_time), leave_type = VALUES(leave_type)
    </insert>


    <select id="getUserLeaveList" resultType="com.qmqb.imp.system.domain.vo.UserLeaveVo">
        select  dept_name,dept_id,start_time,end_time,nick_name,user_id
        from tb_user_leave
        where status = '1'
        and nick_name = #{username}
        and leave_type = #{leaveType}
        and (
        (start_time >= #{startDate} AND start_time <![CDATA[ <= ]]> #{endDate})
        OR
        (end_time >= #{startDate} AND end_time <![CDATA[ <= ]]> #{endDate})
        OR
        (start_time <![CDATA[ < ]]> #{startDate} AND end_time > #{endDate})
        )
    </select>



</mapper>
