package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PerformanceFeedbackAuditStatusEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackDataSourceEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEventBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainBo;
import com.qmqb.imp.system.domain.performance.PerformanceEvent;
import com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo;
import com.qmqb.imp.system.mapper.performance.PerformanceEventMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceEventService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo;
import com.qmqb.imp.system.domain.dto.PerformanceDistributionDTO;
import com.qmqb.imp.system.domain.dto.PerformanceEventStatusDTO;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;

/**
 * 绩效事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class PerformanceEventServiceImpl extends ServiceImpl<PerformanceEventMapper, PerformanceEvent> implements IPerformanceEventService {

    public static final String LINE_BREAK = "\n";
    public static final String DEFALUT_VALUE = "-";
    private final PerformanceEventMapper baseMapper;
    private final PerformanceFeedbackCodeGenerator codeGenerator;
    private final ISysUserService sysUserService;
    private final PerformanceFeedbackValidationService validationService;
    private final IPerformanceFeedbackMainService performanceFeedbackMainService;
    private final IPerformanceFeedbackService performanceFeedbackService;

    /**
     * 查询绩效事件
     */
    @Override
    public PerformanceEventVo queryById(Long id){
        PerformanceEventVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            // 1. 查所有主表
            PerformanceFeedbackMainQueryBo queryBo = new PerformanceFeedbackMainQueryBo();
            queryBo.setEventId(id);
            List<PerformanceFeedbackMainVo> mainVoList = performanceFeedbackMainService.queryList(queryBo);
            // 2. 汇总所有主表id
            List<Long> mainIds = new ArrayList<>();
            if (mainVoList != null && !mainVoList.isEmpty()) {
                for (PerformanceFeedbackMainVo mainVo : mainVoList) {
                    if (mainVo.getId() != null) {
                        mainIds.add(mainVo.getId());
                    }
                }
            }
            // 3. 批量查所有明细
            List<PerformanceFeedbackVo> feedbackList = new ArrayList<>();
            if (!mainIds.isEmpty()) {
                PerformanceFeedbackBo feedbackBo = new PerformanceFeedbackBo();
                feedbackBo.setMainFeedbackIds(mainIds);
                feedbackList = performanceFeedbackService.queryList(feedbackBo);
            }
            vo.setPerformanceFeedbackList(feedbackList);
            handlePerformanceDistribution(Collections.singletonList(vo));
        }
        return vo;
    }

    /**
     * 查询绩效事件列表
     */
    @Override
    public TableDataInfo<PerformanceEventVo> queryPageList(PerformanceEventBo bo, PageQuery pageQuery) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("当前用户信息不存在");
        }
        // 设置权限相关字段
        bo.setIsAdmin(currentUser.isAdmin());
        bo.setIsJszxAdmin(currentUser.isJszxAdmin());
        bo.setIsProjectManager(currentUser.isProjectManager());
        bo.setUserName(currentUser.getUserName());

        if (ObjUtil.isEmpty(pageQuery.getOrderByColumn())){
            // 默认按反馈时间降序排序
            pageQuery.setOrderByColumn("feedbackTime");
            pageQuery.setIsAsc("desc");
        }
        // 调用自定义分页SQL
        IPage<PerformanceEventVo> result = baseMapper.selectEventPageByCondition(pageQuery.build(), bo);
        // 批量查询绩效分布
        handlePerformanceDistribution(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void handlePerformanceDistribution(List<PerformanceEventVo> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        List<Long> eventIds = extractEventIds(records);
        Set<String> submitterSet = extractSubmitters(records);
        if (eventIds.isEmpty()) {
            return;
        }
        Map<Long, PerformanceDistributionDTO> distMap = getPerformanceDistributionMap(eventIds);
        Map<Long, PerformanceEventStatusDTO> statusMap = getEventStatusMap(eventIds);
        Map<String, String> submitterNameMap = getSubmitterNameMap(submitterSet);
        Map<String, String> createByNameMap = getCreateByNameMap(records);
        for (PerformanceEventVo vo : records) {
            setPerformanceDistribution(vo, distMap.get(vo.getId()));
            setEventStatus(vo, statusMap.get(vo.getId()));
            setSubmitterName(vo, submitterNameMap);
            setCreateByCn(vo,createByNameMap);
        }
    }

    /**
     * 提取事件ID列表
     */
    private List<Long> extractEventIds(List<PerformanceEventVo> records) {
        List<Long> eventIds = new ArrayList<>();
        for (PerformanceEventVo vo : records) {
            if (vo.getId() != null) {
                eventIds.add(vo.getId());
            }
        }
        return eventIds;
    }

    /**
     * 提取提交人集合
     */
    private Set<String> extractSubmitters(List<PerformanceEventVo> records) {
        Set<String> submitterSet = new HashSet<>();
        for (PerformanceEventVo vo : records) {
            if (StringUtils.isNotBlank(vo.getSubmitter())) {
                submitterSet.add(vo.getSubmitter());
            }
        }
        return submitterSet;
    }

    /**
     * 查询绩效分布Map
     */
    private Map<Long, PerformanceDistributionDTO> getPerformanceDistributionMap(List<Long> eventIds) {
        List<PerformanceDistributionDTO> distList = baseMapper.selectPerformanceDistributionByEventIds(eventIds);
        Map<Long, PerformanceDistributionDTO> distMap = new HashMap<>(16);
        if (distList != null) {
            for (PerformanceDistributionDTO dto : distList) {
                distMap.put(dto.getEventId(), dto);
            }
        }
        return distMap;
    }

    /**
     * 查询审核状态Map
     */
    private Map<Long, PerformanceEventStatusDTO> getEventStatusMap(List<Long> eventIds) {
        List<PerformanceEventStatusDTO> statusList = baseMapper.selectEventStatusByEventIds(eventIds);
        Map<Long, PerformanceEventStatusDTO> statusMap = new HashMap<>(16);
        if (statusList != null) {
            for (PerformanceEventStatusDTO statusDTO : statusList) {
                statusMap.put(statusDTO.getEventId(), statusDTO);
            }
        }
        return statusMap;
    }

    /**
     * 查询提交人中文名Map
     */
    private Map<String, String> getSubmitterNameMap(Set<String> submitterSet) {
        Map<String, String> submitterNameMap = new HashMap<>(16);
        if (submitterSet != null && !submitterSet.isEmpty()) {
            for (String submitter : submitterSet) {
                String submitterName = "-";
                if (StringUtils.isNotBlank(submitter)) {
                    SysUser user = sysUserService.selectUserByUserName(submitter);
                    if (user != null && StringUtils.isNotBlank(user.getNickName())) {
                        submitterName = user.getNickName();
                    }
                }
                submitterNameMap.put(submitter, submitterName);
            }
        }
        return submitterNameMap;
    }

    /**
     * 查询创建人的中文名称Map
     * @param records
     * @return
     */
    private Map<String, String> getCreateByNameMap(List<PerformanceEventVo> records) {
        Map<String, String> createByNameMap = new HashMap<>(16);
        if (records != null && !records.isEmpty()) {
            List<SysUser> sysUsers = sysUserService.selectAllUser2();
            for (PerformanceEventVo record : records) {
                String createBy = record.getCreateBy();
                String createByName = sysUsers.stream().filter(user -> StringUtils.equals(user.getUserName(), createBy)).map(SysUser::getNickName).findFirst().orElse(null);
                if ("admin".equals(createBy) && StringUtils.isBlank(createByName)) {
                    createByName = "超级管理员";
                }
                createByNameMap.put(createBy, createByName);
            }
        }
        return createByNameMap;
    }

    /**
     * 设置绩效分布
     */
    private void setPerformanceDistribution(PerformanceEventVo vo, PerformanceDistributionDTO dto) {
        if (dto != null) {
            StringBuilder sb = new StringBuilder();
            if (dto.getSCount() != null && dto.getSCount() > 0) {
                sb.append("S：").append(dto.getSCount()).append("人\n");
            }
            if (dto.getACount() != null && dto.getACount() > 0) {
                sb.append("A：").append(dto.getACount()).append("人\n");
            }
            if (dto.getCCount() != null && dto.getCCount() > 0) {
                sb.append("C：").append(dto.getCCount()).append("人\n");
            }
            if (dto.getDCount() != null && dto.getDCount() > 0) {
                sb.append("D：").append(dto.getDCount()).append("人");
            }
            String string = sb.toString();
            if (StringUtils.isBlank(string)) {
                vo.setPerformanceDistribution("无分布");
            } else {
                if (string.endsWith(LINE_BREAK)) {
                    string = string.substring(0, string.length() - 1);
                }
                vo.setPerformanceDistribution(string);
            }
        } else {
            vo.setPerformanceDistribution("无分布");
        }
    }

    /**
     * 设置审核状态
     */
    private void setEventStatus(PerformanceEventVo vo, PerformanceEventStatusDTO statusDTO) {
        if (statusDTO != null) {
            vo.setSubmitStatus(statusDTO.getSubmitStatus());
            vo.setProjectManagerAuditStatus(statusDTO.getProjectManagerAuditStatus());
            vo.setFinalAudit(statusDTO.getFinalAudit());
        }
    }

    /**
     * 设置提交人中文名
     */
    private void setSubmitterName(PerformanceEventVo vo, Map<String, String> submitterNameMap) {
        String submitter = vo.getSubmitter();
        String submitterName = submitterNameMap.getOrDefault(submitter, DEFALUT_VALUE);
        if (PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode().equals(vo.getSubmitStatus()) && DEFALUT_VALUE.equals(submitterName)){
            submitterName="系统";
        }
        vo.setSubmitterName(submitterName);
    }

    /**
     * 设置创建人中文名
     */
    private void setCreateByCn(PerformanceEventVo vo, Map<String, String> createByNameMap) {
        String createBy = vo.getCreateBy();
        String createByName = createByNameMap.getOrDefault(createBy, "系统");
        vo.setCreateByCn(createByName);
    }

    /**
     * 查询绩效事件列表
     */
    @Override
    public List<PerformanceEventVo> queryList(PerformanceEventBo bo) {
        LambdaQueryWrapper<PerformanceEvent> lqw = buildQueryWrapper(bo);
        List<PerformanceEventVo> performanceEventVos = baseMapper.selectVoList(lqw);
        handlePerformanceDistribution(performanceEventVos);
        return performanceEventVos;
    }

    private LambdaQueryWrapper<PerformanceEvent> buildQueryWrapper(PerformanceEventBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PerformanceEvent> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackCode()), PerformanceEvent::getFeedbackCode, bo.getFeedbackCode());
        lqw.eq(bo.getYear() != null, PerformanceEvent::getYear, bo.getYear());
        lqw.eq(bo.getMonth() != null, PerformanceEvent::getMonth, bo.getMonth());
        lqw.eq(bo.getFeedbackTime() != null, PerformanceEvent::getFeedbackTime, bo.getFeedbackTime());
        lqw.eq(StringUtils.isNotBlank(bo.getEventTitle()), PerformanceEvent::getEventTitle, bo.getEventTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getEventDetail()), PerformanceEvent::getEventDetail, bo.getEventDetail());
        lqw.eq(bo.getEventStartTime() != null, PerformanceEvent::getEventStartTime, bo.getEventStartTime());
        lqw.eq(bo.getEventEndTime() != null, PerformanceEvent::getEventEndTime, bo.getEventEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDataSource()), PerformanceEvent::getDataSource, bo.getDataSource());
        lqw.eq(StringUtils.isNotBlank(bo.getSubmitStatus()), PerformanceEvent::getSubmitStatus, bo.getSubmitStatus());
        lqw.eq(bo.getSubmitTime() != null, PerformanceEvent::getSubmitTime, bo.getSubmitTime());
        lqw.eq(StringUtils.isNotBlank(bo.getSubmitter()), PerformanceEvent::getSubmitter, bo.getSubmitter());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectManagerAuditStatus()), PerformanceEvent::getProjectManagerAuditStatus, bo.getProjectManagerAuditStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getFinalAudit()), PerformanceEvent::getFinalAudit, bo.getFinalAudit());
        return lqw;
    }

    /**
     * 新增绩效事件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PerformanceEventBo bo) {
        // 生成统一编码、时间等
        LocalDate now = LocalDate.now();
        int evalYear = now.getYear();
        int evalMonth = now.getMonthValue();
        Date date = new Date();
        String feedbackCode = codeGenerator.generateFeedbackCode();

        // 1. 插入事件表，先拿到事件ID
        bo.setFeedbackCode(feedbackCode);
        bo.setYear(evalYear);
        bo.setMonth(evalMonth);
        bo.setFeedbackTime(date);
        bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
        bo.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode());

        PerformanceEvent add = BeanUtil.toBean(bo, PerformanceEvent.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        } else {
            return false;
        }

        // 2. 循环feedbackList，生成多个主表BO并插入，eventId赋值
        if (bo.getFeedbackList() != null && !bo.getFeedbackList().isEmpty()) {
            for (PerformanceFeedbackBo feedbackBo : bo.getFeedbackList()) {
                PerformanceFeedbackMainBo mainBo = new PerformanceFeedbackMainBo();
                // 字段映射
                mainBo.setEventId(add.getId());
                mainBo.setFeedbackCode(feedbackCode);
                mainBo.setEventTitle(bo.getEventTitle());
                mainBo.setEventDetail(bo.getEventDetail());
                mainBo.setEventStartTime(bo.getEventStartTime());
                mainBo.setEventEndTime(bo.getEventEndTime());
                mainBo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
                mainBo.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode());
                mainBo.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
                mainBo.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
                mainBo.setPrimaryIndicator(feedbackBo.getPrimaryIndicator());
                mainBo.setSecondaryIndicator(feedbackBo.getSecondaryIndicator());
                mainBo.setFinalAudit(bo.getFinalAudit());
                mainBo.setRemark(bo.getRemark());
                mainBo.setSubmitTime(bo.getSubmitTime());
                mainBo.setSubmitter(bo.getSubmitter());
                mainBo.setProjectManagerAuditor(bo.getProjectManagerAuditor());
                mainBo.setFeedbackCode(feedbackCode);
                mainBo.setYear(evalYear);
                mainBo.setMonth(evalMonth);
                mainBo.setFeedbackTime(date);
                // 只放当前一个feedback
                mainBo.setFeedbackList(Collections.singletonList(feedbackBo));
                performanceFeedbackMainService.insertByBo(mainBo);
            }
        }
        // 如果是移动端提交，默认提交状态为已提交
        if (!ObjUtil.isEmpty(bo.getIsMobile()) && bo.getIsMobile()){
            String submitter = LoginHelper.getUsername();
            ((PerformanceEventServiceImpl) AopContext.currentProxy()).batchSubmit(Collections.singleton(add.getId()), submitter);
        }
        return true;
    }

    /**
     * 修改绩效事件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PerformanceEventBo bo) {
        // 1. 更新事件表
        PerformanceEvent update = BeanUtil.toBean(bo, PerformanceEvent.class);
        validEntityBeforeSave(update);
        boolean eventFlag = baseMapper.updateById(update) > 0;
        if (!eventFlag) {
            return false;
        }

        // 2. 查询所有eventId对应的主表记录
        PerformanceFeedbackMainQueryBo queryBo = new PerformanceFeedbackMainQueryBo();
        queryBo.setEventId(bo.getId());
        List<PerformanceFeedbackMainVo> mainVoList = performanceFeedbackMainService.queryList(queryBo);
        List<PerformanceFeedbackBo> feedbackList = bo.getFeedbackList();

        // 3. 构建Map，key为id
        Map<Long, PerformanceFeedbackMainVo> mainVoMap = new HashMap<>(16);
        if (mainVoList != null) {
            for (PerformanceFeedbackMainVo mainVo : mainVoList) {
                mainVoMap.put(mainVo.getId(), mainVo);
            }
        }

        Map<Long, PerformanceFeedbackBo> feedbackBoMap = new HashMap<>(16);
        if (feedbackList != null) {
            for (PerformanceFeedbackBo feedbackBo : feedbackList) {
                feedbackBoMap.put(feedbackBo.getMainFeedbackId(), feedbackBo);
            }
        }

        // 4. 更新和新增
        if (feedbackList != null) {
            for (PerformanceFeedbackBo feedbackBo : feedbackList) {
                Long id = feedbackBo.getMainFeedbackId();
                PerformanceFeedbackMainVo mainVo = mainVoMap.get(id);
                PerformanceFeedbackMainBo mainBo = new PerformanceFeedbackMainBo();
                if (mainVo != null) {
                    // 更新
                    mainBo.setId(mainVo.getId());
                    mainBo.setFeedbackCode(mainVo.getFeedbackCode());
                } else {
                    // 新增
                    mainBo.setFeedbackCode(bo.getFeedbackCode());
                }
                mainBo.setEventId(bo.getId());
                mainBo.setPrimaryIndicator(feedbackBo.getPrimaryIndicator());
                mainBo.setSecondaryIndicator(feedbackBo.getSecondaryIndicator());
                mainBo.setEventTitle(bo.getEventTitle());
                mainBo.setEventDetail(bo.getEventDetail());
                mainBo.setEventStartTime(bo.getEventStartTime());
                mainBo.setEventEndTime(bo.getEventEndTime());
                mainBo.setDataSource(bo.getDataSource());
                mainBo.setSubmitStatus(bo.getSubmitStatus());
                mainBo.setProjectManagerAuditStatus(bo.getProjectManagerAuditStatus());
                mainBo.setProjectManagerAuditor(bo.getProjectManagerAuditor());
                mainBo.setFinalAudit(bo.getFinalAudit());
                mainBo.setRemark(bo.getRemark());
                mainBo.setSubmitTime(bo.getSubmitTime());
                mainBo.setSubmitter(bo.getSubmitter());
                mainBo.setYear(bo.getYear());
                mainBo.setMonth(bo.getMonth());
                mainBo.setFeedbackTime(bo.getFeedbackTime());
                mainBo.setFeedbackList(Collections.singletonList(feedbackBo));
                if (mainVo != null) {
                    performanceFeedbackMainService.updateByBo(mainBo);
                } else {
                    performanceFeedbackMainService.insertByBo(mainBo);
                }
            }
        }

        // 5. 删除多余主表
        if (mainVoList != null && !mainVoList.isEmpty()) {
            List<Long> deleteIds = new ArrayList<>();
            for (PerformanceFeedbackMainVo mainVo : mainVoList) {
                if (feedbackBoMap.get(mainVo.getId()) == null) {
                    deleteIds.add(mainVo.getId());
                }
            }
            if (!deleteIds.isEmpty()) {
                // 调用实现类，确保主表和明细都被删除
                performanceFeedbackMainService.deleteWithValidByIds(deleteIds, true);
            }
        }
        return true;
    }

    /**
     * 批量提交绩效事件（实际提交其下所有主表）
     *
     * @param eventIds 事件ID集合
     * @param submitter 提交人
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSubmit(Collection<Long> eventIds, String submitter) {
        if (eventIds == null || eventIds.isEmpty()) {
            return false;
        }
        // 1. 查询所有事件下的主表ID
        List<Long> mainIds = new ArrayList<>();
        for (Long eventId : eventIds) {
            PerformanceFeedbackMainQueryBo queryBo = new PerformanceFeedbackMainQueryBo();
            queryBo.setEventId(eventId);
            List<PerformanceFeedbackMainVo> mainVoList = performanceFeedbackMainService.queryList(queryBo);
            if (mainVoList != null && !mainVoList.isEmpty()) {
                for (PerformanceFeedbackMainVo mainVo : mainVoList) {
                    if (mainVo.getId() != null) {
                        mainIds.add(mainVo.getId());
                    }
                }
            }
        }
        if (mainIds.isEmpty()) {
            return false;
        }
        // 2. 调用主表批量提交
        boolean mainResult = performanceFeedbackMainService.batchSubmit(mainIds, submitter);
        // 3. 同步更新事件表的提交状态、提交时间、提交人
        Date now = new Date();
        for (Long eventId : eventIds) {
            PerformanceEvent event = baseMapper.selectById(eventId);
            if (event != null) {
                event.setSubmitTime(now);
                event.setSubmitter(submitter);
                baseMapper.updateById(event);
            }
        }
        return mainResult;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceEvent entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效事件
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        // 先删除所有 eventId 关联的主表和明细
        if (ids != null && !ids.isEmpty()) {
            PerformanceFeedbackMainQueryBo queryBo = new PerformanceFeedbackMainQueryBo();
            for (Long eventId : ids) {
                queryBo.setEventId(eventId);
                List<PerformanceFeedbackMainVo> mainVoList = performanceFeedbackMainService.queryList(queryBo);
                if (mainVoList != null && !mainVoList.isEmpty()) {
                    List<Long> mainIds = new ArrayList<>();
                    for (PerformanceFeedbackMainVo mainVo : mainVoList) {
                        mainIds.add(mainVo.getId());
                    }
                    performanceFeedbackMainService.deleteWithValidByIds(mainIds, true);
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
