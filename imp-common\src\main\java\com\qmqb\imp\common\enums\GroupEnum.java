package com.qmqb.imp.common.enums;

import com.qmqb.imp.common.constant.CommConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2022/12/6
 */
@Getter
@AllArgsConstructor
public enum GroupEnum {

    ALL(0, "技术中心"),
    ZIYINGZICHAN(1, "资产运营开发组"),
    XIANGMU(2, "项目管理组"),
    YUNWEI(3, "运维组"),
    CAIWU(4, "财务开发一组"),
    SHUJU(5, "数据管理组"),
    HAIWAI(6, "海外开发组"),
    FENGKONGDAIHOU(7, "风控贷后测试组"),
    BI(8, "BI组"),
    QIANDUAN(9, "前端开发组"),
    ANDROID(10, "Android开发组"),
    JIAGOU(11, "架构开发组"),
    HAIWAICESHI(12, "海外测试组"),
    ZIJIN(13, "资金开发组"),
    ZHUDAI(14, "助贷测试组"),
    IOS(15, "IOS开发组"),
    YINLIU(16, "引流资产开发组"),
    FENGKONG(17, "风控开发组"),
    DIANSHANGCUISHOU(18, "电销催收开发组"),
    CAIWU2(19, "财务开发二组")
    ;

    private final Integer type;
    private final String groupName;

    public static List<String> getGroupNameListByType(Integer type) {
        if (Objects.equals(type, CommConstants.CommonVal.ZERO)) {
            return Arrays.stream(GroupEnum.values()).filter(value -> !Objects.equals(value.getType(), CommConstants.CommonVal.ZERO)).map(GroupEnum::getGroupName).collect(Collectors.toList());
        }
        for (GroupEnum value : GroupEnum.values()) {
            if (value.getType().equals(type)) {
                return Collections.singletonList(value.getGroupName());
            }
        }
        return null;
    }

    public static String getGroupNameByType(Integer type) {
        return Arrays.stream(values())
            .filter(groupTypeEnum -> groupTypeEnum.getType().equals(type))
            .findFirst()
            .map(GroupEnum::getGroupName)
            .orElse(null);
    }

    public static void main(String[] args) {
        System.out.println(Arrays.stream(GroupEnum.values()).map(GroupEnum::getGroupName).collect(Collectors.toList()));
    }
}
