# 钉钉预警功能实现文档

## 概述

本文档详细介绍了基于 Spring Boot + XXL-JOB + 钉钉机器人的定时预警功能实现，包含完整的代码实现、配置和依赖管理。该功能支持定时检查业务数据并通过钉钉机器人发送预警消息。

## 1. 项目依赖配置

### 1.1 Maven 依赖版本定义（pom.xml）

```xml
<properties>
    <!-- 基础版本 -->
    <spring-boot.version>2.7.6</spring-boot.version>
    <java.version>1.8</java.version>
    
    <!-- 核心依赖版本 -->
    <xxl-job.version>2.2.0</xxl-job.version>
    <hutool.version>5.8.10</hutool.version>
    <dingtalk-oapi.version>2.0.0</dingtalk-oapi.version>
    <dingtalk-api.version>2.1.12</dingtalk-api.version>
    <mybatis-plus.version>3.5.2</mybatis-plus.version>
    <lombok.version>1.18.24</lombok.version>
</properties>
```

### 1.2 依赖管理（dependencyManagement）

```xml
<dependencyManagement>
    <dependencies>
        <!-- Spring Boot BOM -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>

        <!-- Hutool 工具包 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-bom</artifactId>
            <version>${hutool.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>

        <!-- XXL-JOB 定时任务 -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job.version}</version>
        </dependency>

        <!-- 钉钉旧版服务端API -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>${dingtalk-oapi.version}</version>
        </dependency>

        <!-- 钉钉新版服务端API -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>${dingtalk-api.version}</version>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 1.3 模块依赖（具体模块的 pom.xml）

```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <!-- Spring Boot Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- XXL-JOB 核心依赖 -->
    <dependency>
        <groupId>com.xuxueli</groupId>
        <artifactId>xxl-job-core</artifactId>
    </dependency>

    <!-- 钉钉旧版服务端API -->
    <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>alibaba-dingtalk-service-sdk</artifactId>
    </dependency>

    <!-- 钉钉新版服务端API -->
    <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>dingtalk</artifactId>
    </dependency>

    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>

    <!-- Hutool 工具包 -->
    <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
    </dependency>

    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>

    <!-- JSON 处理 -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
    </dependency>

    <!-- 参数校验 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
</dependencies>
```

## 2. 配置文件

### 2.1 application-dev.properties

```properties
# 消息中心地址
message.url=http://192.168.81.152:7711/message-center-api/v1/message/sendBase
message.appKey=impEOdzG2yK
message.appSecret=43fd5ffdcdb47952d3477582d265661a7bc38c01

# 钉钉配置，全民钱包公司应用
dingtalk.app-key=dingzdbsspnvbrcddwzs
dingtalk.app-secret=ljf0G7Z7_5LOQaJ-hI_45BwfxA3cYb4gW6apoT8Z41BmvKZuP3F2LUH6h6R8Zgjp

# 钉钉机器人地址
dingtalk.robot-url=https://oapi.dingtalk.com/robot/send?access_token=ff26a89517ba859f217f26b3e2e2b8f070e9391101948de2ae8a31d1c53f197d
dingtalk.pm-robot-url=https://oapi.dingtalk.com/robot/send?access_token=ff26a89517ba859f217f26b3e2e2b8f070e9391101948de2ae8a31d1c53f197d
dingtalk.jszx-robot-url=https://oapi.dingtalk.com/robot/send?access_token=ff26a89517ba859f217f26b3e2e2b8f070e9391101948de2ae8a31d1c53f197d

# XXL-JOB 配置
xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
xxl.job.executor.appname=your-app-name
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
xxl.job.executor.logretentiondays=30
xxl.job.accessToken=
```

## 3. 核心代码实现

### 3.1 钉钉配置类

```java
package com.qmqb.imp.job.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 钉钉配置
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {
    /**
     * 全民钱包应用的唯一标识key
     */
    private String appKey;
    
    /**
     * 全民钱包应用的密钥
     */
    private String appSecret;
    
    /**
     * 006.内部管理系统发布 模板编码
     */
    private String systemReleaseCode;
    
    /**
     * 020.系统故障处理 模板编码
     */
    private String systemFailureCode;

    /**
     * 组长机器人地址
     */
    private String robotUrl;

    /**
     * 项管机器人地址
     */
    private String pmRobotUrl;

    /**
     * 技术中心机器人地址
     */
    private String jszxRobotUrl;

    /**
     * 绩效反馈appKey
     */
    private String feedBackAppKey;
    
    /**
     * 绩效反馈appSecret
     */
    private String feedBackAppSecret;
}
```

### 3.2 消息中心配置类

```java
package com.qmqb.imp.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 消息中心的配置
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "message")
public class MessageProperties {
    private String url;
    private String appKey;
    private String appSecret;
}
```

### 3.3 消息通道类型枚举

```java
package com.qmqb.imp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息通知渠道类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageChannelTypeEnum {

    /**
     * JPUSH
     */
    JPUSH("0", "JPUSH"),

    /**
     * 短信
     */
    SHORT_MESSAGE("1", "短信"),

    /**
     * 钉钉
     */
    DING_DING_MESSAGE("2", "钉钉"),

    /**
     * 邮件
     */
    EMAIL("3", "邮件"),

    /**
     * 钉钉工作通知
     */
    DING_DING_NOTICE("4", "钉钉工作通知"),
    ;

    private final String type;
    private final String desc;
}
```

### 3.4 消息基础类

```java
package com.qmqb.imp.system.domain.bo.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
public class BaseMsgBo {

    /**
     * 渠道类型：0-JPush，1-短信，2-钉钉，3-邮件，4-钉钉工作通知
     */
    @JsonIgnore
    @NotBlank(message = "渠道类型不能为空")
    private String channelType;
}
```

### 3.5 钉钉机器人消息对象

```java
package com.qmqb.imp.system.domain.bo.message;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 钉钉机器人
 * <AUTHOR>
 * @since 2023-01-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DdRobotMsgBo extends BaseMsgBo {

    /**
     * 消息通知地址，webhook
     */
    @NotBlank(message = "消息通知地址，webhook不能为空")
    private String url;

    /**
     * 消息类型:text文本
     */
    @NotBlank(message = "消息类型:text文本不能为空")
    private String msgtype;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;

    /**
     * 是否@全部人：false-否，true-是
     */
    private String isAtAll;

    /**
     * 需要@的联系人组
     */
    private List<String> atMobiles;

    /**
     * 钉钉消息是否需要加签：0-否，1-是
     */
    private Integer isSign;
}
```

### 3.6 消息服务接口

```java
package com.qmqb.imp.system.service.message;

import com.qmqb.imp.system.domain.bo.message.BaseMsgBo;
import com.qmqb.imp.system.domain.bo.message.response.MessageResponse;

/**
 * 消息中心服务类
 * <AUTHOR>
 * @since 2023-01-29
 */
public interface IMessageService {

    /**
     * 发送消息
     * @param baseMsgBo
     * @return
     */
    MessageResponse<Void> sendBase(BaseMsgBo baseMsgBo);
}
```

### 3.7 消息服务实现类

```java
package com.qmqb.imp.system.service.impl.message;

import cn.hutool.crypto.SignUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.imp.common.config.MessageProperties;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.ValidatorUtils;
import com.qmqb.imp.common.utils.spring.SpringUtils;
import com.qmqb.imp.system.domain.bo.message.BaseMsgBo;
import com.qmqb.imp.system.domain.bo.message.SendBaseRequest;
import com.qmqb.imp.system.domain.bo.message.response.MessageResponse;
import com.qmqb.imp.system.service.message.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-01-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MessageServiceImpl implements IMessageService {

    private final MessageProperties messageProperties;

    /**
     * 生产环境CODE
     */
    public static final String PROD = "prod";

    @Override
    public MessageResponse<Void> sendBase(BaseMsgBo baseMsgBo) {
        //校验必填参数
        ValidatorUtils.validate(baseMsgBo);

        //签名
        SendBaseRequest sendBaseRequest = SendBaseRequest.builder()
            .appKey(messageProperties.getAppKey())
            .messageContent(JacksonUtil.toJson(baseMsgBo))
            .channelType(baseMsgBo.getChannelType())
            .timestamp(System.currentTimeMillis())
            .thirdTaskNo(IdUtil.getIdStr())
            .build();
        sendBaseRequest.setSign(SignUtil.signParams(DigestAlgorithm.MD5,
            JacksonUtil.toMap(JacksonUtil.toJson(sendBaseRequest)),
            "&", "=", true,
            "&app_secret=" + messageProperties.getAppSecret()));

        log.info("消息中心请求参数:{}", JacksonUtil.toJson(sendBaseRequest));

        String response = "{\"code\":200,\"msg\":\"测试环境，不开启真实发送\",\"data\":null,\"success\":true}";
        if (PROD.equals(SpringUtils.getActiveProfile())) {
            response = HttpUtil.post(messageProperties.getUrl(), JacksonUtil.toJson(sendBaseRequest));
        }

        log.info("消息中心响应参数:{}", response);
        return JSON.parseObject(response, new TypeReference<MessageResponse<Void>>() {});
    }
}
```

### 3.8 常量定义

```java
package com.qmqb.imp.common.constant;

/**
 * 通用常量信息
 */
public class Constants {

    /**
     * 代码质量问题预警
     */
    String CODE_QUALITY_WARN_TAG = "目前统计代码问题待处理情况如下：\n{groupList}\n请以上组尽快安排人员到【绩效系统-代码管理-代码质量管理】进行处理，谢谢！";

    /**
     * 慢sql问题预警
     */
    String SLOW_SQL_WARN_TAG = "目前统计暂未处理的生产慢SQL如下：\n{groupList}\n请负责以上数据库的各组尽快安排人员到【绩效系统-工作管理-生产慢SQL管理】进行处理，谢谢！";

    /**
     * 组内工时差距超出阈值预警
     */
    String WORK_HOUR_GAP_TAG = "【工时预警】目前统计工时相差大于40的组名单：{groupList}！";

    /**
     * 组内工时正常预警
     */
    String WORK_HOUR_NORMAL_TAG = "【工时预警】目前统计工时正常！";
}
```

### 3.9 节假日工具类

```java
package com.qmqb.imp.system.runner;

import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.DateUtils;
import org.springframework.http.HttpStatus;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 节假日工具类
 */
public class HolidayUtil {

    /**
     * 法定节假日列表（需要根据实际情况配置）
     */
    private static List<String> lawHolidayList = Arrays.asList(
        "2024-01-01", "2024-02-10", "2024-02-11", "2024-02-12",
        "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-17"
        // ... 更多节假日
    );

    /**
     * 需要补班的周末列表
     */
    private static List<String> extraWorkdayList = Arrays.asList(
        "2024-02-04", "2024-02-18"
        // ... 更多补班日
    );

    /**
     * 获取法定节假日列表
     */
    public static List<String> getLawHolidayList() {
        return lawHolidayList;
    }

    /**
     * 获取需要补班的周末列表
     */
    public static List<String> getExtraWorkdayList() {
        return extraWorkdayList;
    }

    /**
     * 判断是否是法定假日
     */
    public static boolean isLawHoliday(String dateStr) {
        return getLawHolidayList().contains(dateStr);
    }

    /**
     * 判断是否是周末
     */
    public static boolean isWeekends(String dateStr) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD);
        Date date = sdf.parse(dateStr);
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        return ca.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY
            || ca.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY;
    }

    /**
     * 判断是否是需要额外补班的周末
     */
    public static boolean isExtraWorkday(String dateStr) {
        return getExtraWorkdayList().contains(dateStr);
    }

    /**
     * 判断是否节假日（包含法定节假日和不需要补班的周末）
     *
     * @param dateStr YYYY_MM_DD时间格式
     * @return
     */
    public static boolean isHoliday(String dateStr) {
        try {
            // 首先法定节假日必定是休息日
            if (isLawHoliday(dateStr)) {
                return true;
            }
            // 排除法定节假日外的非周末必定是工作日
            if (!isWeekends(dateStr)) {
                return false;
            }
            // 所有周末中只有非补班的才是休息日
            if (isExtraWorkday(dateStr)) {
                return false;
            }
        } catch (Exception e) {
            throw new ServiceException(String.format("%s判断是否节假日的解析异常", dateStr), HttpStatus.HTTP_INTERNAL_ERROR);
        }
        return true;
    }
}
```

## 4. 核心业务实现

### 4.1 定时任务服务类

```java
package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ScanProjectFileMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代码质量问题通知定时器
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CodeQualityWarnService {

    private final ScanProjectFileMapper scanProjectFileMapper;
    private final ISysDeptService sysDeptService;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    @TraceId("代码质量问题通知定时任务")
    @XxlJob("codeQualityWarnServiceJobHandler")
    public ReturnT<String> codeQualityWarnServiceJobHandler(String param) {
        try {
            //节假日不执行定时任务
            if(HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("开始执行代码质量问题通知定时任务...");
            log.info("开始执行代码质量问题通知定时任务");
            val sw = new StopWatch();
            sw.start();

            // 获取技术中心下的所有子部门
            List<Long> groupIdList = sysDeptService.listTecCenterDeptIdList();
            // 过滤新员工组
            groupIdList = groupIdList.stream()
                    .filter(groupId -> !UserConstants.NEW_MEM_DEPT_ID.equals(groupId))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(groupIdList)) {
                log.info("未找到技术中心部门信息");
                return ReturnT.SUCCESS;
            }

            // 构建部门ID到部门名称的映射
            Map<Long, String> deptIdToNameMap = new HashMap<>(16);
            for (Long deptId : groupIdList) {
                SysDept dept = sysDeptService.selectDeptById(deptId);
                if (dept != null) {
                    deptIdToNameMap.put(deptId, dept.getDeptName());
                }
            }

            // 统计各组的代码问题数据
            List<String> warnGroupList = new ArrayList<>();

            for (Long deptId : groupIdList) {
                String deptName = deptIdToNameMap.get(deptId);
                if (StrUtil.isBlank(deptName)) {
                    continue;
                }

                // 统计该组的未指派数据（status=0）
                Long unassignedCount = countProblemsByStatusAndDept(deptId, 0);

                // 统计该组的已指派未处理数据（status=1）
                Long assignedUnhandledCount = countProblemsByStatusAndDept(deptId, 1);

                // 只统计有问题的组（两个数据都为0的组不统计）
                if (unassignedCount > 0 || assignedUnhandledCount > 0) {
                    String warnInfo = String.format("%s   已指派未处理：%d条    未指派：%d条",
                            deptName, assignedUnhandledCount, unassignedCount);
                    warnGroupList.add(warnInfo);
                }
            }

            // 发送通知
            if (CollectionUtil.isNotEmpty(warnGroupList)) {
                String groupListStr = String.join("\n", warnGroupList);
                send(Constants.CODE_QUALITY_WARN_TAG, groupListStr);
                XxlJobLogger.log("发送代码质量问题通知，涉及组数：{}", warnGroupList.size());
                log.info("发送代码质量问题通知，涉及组数：{}", warnGroupList.size());
            } else {
                log.info("所有组代码质量问题均已处理完毕，无需发送通知");
                XxlJobLogger.log("所有组代码质量问题均已处理完毕，无需发送通知");
            }

            sw.stop();
            XxlJobLogger.log("代码质量问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("代码质量问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("代码质量问题通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 统计指定部门和状态的问题数量
     *
     * @param deptId 部门ID
     * @param status 状态（0未指派，1已指派未处理，2已指派已处理）
     * @return 问题数量
     */
    private Long countProblemsByStatusAndDept(Long deptId, Integer status) {
        try {
            return scanProjectFileMapper.countByStatusAndDept(String.valueOf(deptId), String.valueOf(status));
        } catch (Exception e) {
            log.error("统计部门{}状态{}的问题数量异常", deptId, status, e);
            return 0L;
        }
    }

    /**
     * 发送预警
     *
     * @param template  模板
     * @param groupList 组列表信息
     */
    private void send(String template, String groupList) {
        Map<String, String> map = new HashMap<>(16);
        map.put("groupList", groupList);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
                .url(dingTalkConfig.getRobotUrl())
                .msgtype("text")
                .content(content)
                .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
```

### 4.2 数据访问层（Mapper）

```java
package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.imp.system.domain.ScanProjectFile;
import org.apache.ibatis.annotations.Param;

/**
 * 扫描项目文件 Mapper 接口
 */
public interface ScanProjectFileMapper extends BaseMapper<ScanProjectFile> {

    /**
     * 统计指定部门和状态的问题文件数量
     * @param deptId 部门ID
     * @param status 状态
     * @return
     */
    Long countByStatusAndDept(@Param("deptId") String deptId, @Param("status") String status);
}
```

### 4.3 Mapper XML 配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ScanProjectFileMapper">

    <select id="countByStatusAndDept" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM tb_scan_project_file spf
        LEFT JOIN tb_scan_project sp ON spf.p_id = sp.p_id
        INNER JOIN tb_project pr ON spf.p_id = pr.p_id
        WHERE spf.del_flag = 0
          AND spf.last_scan_flag = 1
          AND spf.status = #{status}
          AND sp.dev_dept = #{deptId}
          AND sp.last_scan_flag = 1
    </select>

</mapper>
```

## 5. 辅助工具类

### 5.1 参数校验工具类

```java
package com.qmqb.imp.common.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * 参数校验工具类
 */
public class ValidatorUtils {

    private static final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();

    public static <T> void validate(T object) {
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<T>> violations = validator.validate(object);
        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<T> violation : violations) {
                sb.append(violation.getMessage()).append(";");
            }
            throw new IllegalArgumentException(sb.toString());
        }
    }
}
```

### 5.2 Spring 工具类

```java
package com.qmqb.imp.common.utils.spring;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Spring 工具类
 */
@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }

    /**
     * 获取当前激活的配置文件
     */
    public static String getActiveProfile() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        return activeProfiles.length > 0 ? activeProfiles[0] : "default";
    }

    /**
     * 获取Bean
     */
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    /**
     * 获取Bean
     */
    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }
}
```

## 6. 使用示例

### 6.1 创建自定义预警服务

```java
package com.example.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义预警服务示例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomWarnService {

    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    @XxlJob("customWarnJobHandler")
    public ReturnT<String> customWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行自定义预警任务...");

            // 1. 查询需要预警的数据
            List<String> warnDataList = queryWarnData();

            // 2. 判断是否需要发送预警
            if (CollectionUtil.isNotEmpty(warnDataList)) {
                String content = String.join("\n", warnDataList);
                sendWarnMessage("【自定义预警】发现以下问题：\n{content}", content);
                XxlJobLogger.log("发送自定义预警，问题数量：{}", warnDataList.size());
            } else {
                XxlJobLogger.log("无需发送预警");
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("自定义预警任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 查询需要预警的数据
     */
    private List<String> queryWarnData() {
        // 实现具体的数据查询逻辑
        // 例如：查询数据库、调用API等
        return null;
    }

    /**
     * 发送预警消息
     */
    private void sendWarnMessage(String template, String content) {
        Map<String, String> map = new HashMap<>(16);
        map.put("content", content);
        String messageContent = StrUtil.format(template, map);

        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
                .url(dingTalkConfig.getRobotUrl())
                .msgtype("text")
                .content(messageContent)
                .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());

        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("发送预警消息异常", e);
        }
    }
}
```

### 6.2 不同类型的预警消息示例

```java
/**
 * 发送文本消息
 */
public void sendTextMessage(String content) {
    DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getRobotUrl())
            .msgtype("text")
            .content(content)
            .build();
    robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
    messageService.sendBase(robotMsgBo);
}

/**
 * 发送@指定人员的消息
 */
public void sendAtMessage(String content, List<String> atMobiles) {
    DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getRobotUrl())
            .msgtype("text")
            .content(content)
            .atMobiles(atMobiles)
            .build();
    robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
    messageService.sendBase(robotMsgBo);
}

/**
 * 发送@全员的消息
 */
public void sendAtAllMessage(String content) {
    DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getRobotUrl())
            .msgtype("text")
            .content(content)
            .isAtAll("true")
            .build();
    robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
    messageService.sendBase(robotMsgBo);
}
```

## 7. 部署和配置

### 7.1 XXL-JOB 配置

1. **部署 XXL-JOB 调度中心**
   - 下载 XXL-JOB 源码
   - 配置数据库连接
   - 启动调度中心

2. **配置执行器**
   ```properties
   # XXL-JOB 配置
   xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
   xxl.job.executor.appname=your-app-name
   xxl.job.executor.address=
   xxl.job.executor.ip=
   xxl.job.executor.port=9999
   xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
   xxl.job.executor.logretentiondays=30
   xxl.job.accessToken=
   ```

3. **在调度中心添加任务**
   - 任务名称：代码质量预警任务
   - JobHandler：codeQualityWarnServiceJobHandler
   - Cron 表达式：0 0 9 * * ? （每天上午9点执行）

### 7.2 钉钉机器人配置

1. **创建钉钉群机器人**
   - 在钉钉群中添加自定义机器人
   - 选择安全设置（关键词、加签、IP地址）
   - 获取 Webhook 地址

2. **配置机器人地址**
   ```properties
   dingtalk.robot-url=https://oapi.dingtalk.com/robot/send?access_token=YOUR_ACCESS_TOKEN
   ```

### 7.3 消息中心配置

1. **部署消息中心服务**
   - 部署独立的消息中心服务
   - 配置各种消息通道（钉钉、短信、邮件等）

2. **配置消息中心地址**
   ```properties
   message.url=http://your-message-center/v1/message/sendBase
   message.appKey=your-app-key
   message.appSecret=your-app-secret
   ```

## 8. 监控和日志

### 8.1 日志配置

```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/warn-service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/warn-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.qmqb.imp.job.service" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

### 8.2 监控指标

1. **任务执行监控**
   - 任务执行成功率
   - 任务执行耗时
   - 任务失败次数

2. **消息发送监控**
   - 消息发送成功率
   - 消息发送失败原因
   - 消息发送耗时

3. **业务数据监控**
   - 预警数据量统计
   - 预警频率统计
   - 预警类型分布

## 9. 常见问题和解决方案

### 9.1 常见问题

1. **钉钉机器人消息发送失败**
   - 检查 Webhook 地址是否正确
   - 检查安全设置（关键词、加签等）
   - 检查消息格式是否符合要求

2. **定时任务不执行**
   - 检查 XXL-JOB 调度中心连接
   - 检查任务配置和 Cron 表达式
   - 检查执行器注册状态

3. **消息中心调用失败**
   - 检查消息中心服务状态
   - 检查网络连接
   - 检查签名算法和参数

### 9.2 性能优化

1. **数据库查询优化**
   - 添加合适的索引
   - 优化 SQL 查询语句
   - 使用批量查询减少数据库连接

2. **消息发送优化**
   - 异步发送消息
   - 消息发送失败重试机制
   - 消息发送限流

3. **内存优化**
   - 及时释放大对象
   - 使用流式处理大数据量
   - 合理设置 JVM 参数

## 10. 扩展功能

### 10.1 多渠道消息发送

```java
/**
 * 多渠道消息发送服务
 */
@Service
public class MultiChannelMessageService {

    /**
     * 发送钉钉消息
     */
    public void sendDingTalkMessage(String content) {
        // 钉钉消息发送逻辑
    }

    /**
     * 发送短信消息
     */
    public void sendSmsMessage(String phone, String content) {
        // 短信发送逻辑
    }

    /**
     * 发送邮件消息
     */
    public void sendEmailMessage(String email, String subject, String content) {
        // 邮件发送逻辑
    }

    /**
     * 根据配置发送多渠道消息
     */
    public void sendMultiChannelMessage(String content, List<String> channels) {
        for (String channel : channels) {
            switch (channel) {
                case "dingtalk":
                    sendDingTalkMessage(content);
                    break;
                case "sms":
                    // sendSmsMessage(phone, content);
                    break;
                case "email":
                    // sendEmailMessage(email, subject, content);
                    break;
            }
        }
    }
}
```

### 10.2 预警规则配置化

```java
/**
 * 预警规则配置
 */
@Data
@ConfigurationProperties(prefix = "warn.rules")
public class WarnRuleConfig {

    /**
     * 代码质量预警规则
     */
    private CodeQualityRule codeQuality;

    /**
     * 慢SQL预警规则
     */
    private SlowSqlRule slowSql;

    @Data
    public static class CodeQualityRule {
        private boolean enabled = true;
        private int threshold = 0;
        private String cronExpression = "0 0 9 * * ?";
        private List<String> excludeDepts = new ArrayList<>();
    }

    @Data
    public static class SlowSqlRule {
        private boolean enabled = true;
        private int threshold = 5;
        private String cronExpression = "0 0 10 * * ?";
    }
}
```

## 11. 总结

本文档提供了完整的钉钉预警功能实现方案，包括：

1. **完整的依赖配置**：Maven 依赖管理和版本控制
2. **详细的代码实现**：从配置类到业务逻辑的完整实现
3. **实用的工具类**：节假日判断、参数校验等工具类
4. **部署和配置指南**：XXL-JOB、钉钉机器人、消息中心的配置
5. **监控和日志**：完整的监控和日志配置
6. **扩展功能**：多渠道消息发送、规则配置化等扩展方案

使用本文档，你可以在任何 Spring Boot 项目中快速集成钉钉预警功能，实现业务数据的定时监控和预警通知。
