package com.qmqb.imp.system.runner;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.collect.Lists;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.ApplicationContextUtil;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.system.domain.bo.UserLeaveBo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.service.ISysConfigService;
import com.qmqb.imp.system.service.IUserLeaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间操作工具（包含假期，请假）
 *
 * <AUTHOR>
 */
@Slf4j
public class HolidayUtil {

    private static ISysConfigService iSysConfigService;
    private static List<String> extraWorkdayList;
    private static List<String> lawHolidayList;
    private static IUserLeaveService iUserLeaveService;
    private static Integer lunchBreakTime;
    private static final String HOUR_OF_WORKDAY = "7";

    /**
     * 请假时长超过不计入名单
     */
    private static final String OVER_HOUR_TIME = "21";

    static {
        iSysConfigService = ApplicationContextUtil.getBean(ISysConfigService.class);
        iUserLeaveService = ApplicationContextUtil.getBean(IUserLeaveService.class);
    }

    /**
     * 补班的周六日
     */
    public static List<String> getExtraWorkdayList() {

        if (!CollectionUtils.isEmpty(extraWorkdayList)) {
            return extraWorkdayList;
        }

        return Lists.newArrayList(iSysConfigService.selectConfigByKey("extra_workday").split(","));
    }

    /**
     * 节假日
     */
    public static List<String> getLawHolidayList() {
        if (!CollectionUtils.isEmpty(lawHolidayList)) {
            return lawHolidayList;
        }

        return Lists.newArrayList(iSysConfigService.selectConfigByKey("law_holiday").split(","));
    }
    /**
     * 午休时间
     */
    public static Integer getLunchBreakTime() {
        if (lunchBreakTime != null) {
            return lunchBreakTime;
        }
        String breakTimeStr = iSysConfigService.selectConfigByKey("lunch_break_time");
        lunchBreakTime = breakTimeStr != null ? Integer.parseInt(breakTimeStr) : 2;
        return lunchBreakTime;
    }

    /**
     * @description 判断是否是法定假日
     **/
    public static boolean isLawHoliday(String dateStr) {
        if (getLawHolidayList().contains(dateStr)) {
            return true;
        }
        return false;
    }

    /**
     * @description 判断是否是周末
     **/
    public static boolean isWeekends(String dateStr) throws Exception {
        // 先将字符串类型的日期转换为Calendar类型
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD);
        Date date = sdf.parse(dateStr);
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        if (ca.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY
            || ca.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            return true;
        }
        return false;
    }

    /**
     * @description 判断是否是需要额外补班的周末
     **/
    public static boolean isExtraWorkday(String dateStr) {
        if (getExtraWorkdayList().contains(dateStr)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否节假日（包含法定节假日和不需要补班的周末）
     *
     * @param dateStr YYYY_MM_DD时间格式
     * @return
     */
    public static boolean isHoliday(String dateStr) {
        try {
            // 首先法定节假日必定是休息日
            if (isLawHoliday(dateStr)) {
                return true;
            }
            // 排除法定节假日外的非周末必定是工作日
            if (!isWeekends(dateStr)) {
                return false;
            }
            // 所有周末中只有非补班的才是休息日
            if (isExtraWorkday(dateStr)) {
                return false;
            }
        } catch (Exception e) {
            throw new ServiceException(String.format("%s判断是否节假日的解析异常", dateStr), HttpStatus.HTTP_INTERNAL_ERROR);
        }
        return true;
    }

    /**
     * 判断用户是否处于请假
     * @param startDateStr yyyy-MM-dd时间格式
     * @param endDateStr yyyy-MM-dd时间格式
     * @param userId  用户id
     * @return true 请假中  false  非请假
     */
    public static boolean isLeave(String startDateStr, String endDateStr, Long userId) {
        int minCounts = iUserLeaveService.countByUserIdAndTime(userId, startDateStr, endDateStr);
        int maxCounts = iUserLeaveService.countByUserIdInRangeTime(userId, startDateStr, endDateStr);
        return (minCounts > 0) || (maxCounts > 0);
    }


    public static String toDateStr2(LocalDate localDate) {
        if (localDate != null) {
            return DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD).format(localDate);
        }
        return "";
    }

    /**
     * 指定日期添加天数，并且跳过工作日
     *
     * @param date
     * @param workdays
     * @return
     * @throws Exception
     */
    public static LocalDate plusWorkDay(LocalDate date, int workdays) {
        LocalDate result = date;

        int addedDays = 0;
        while (addedDays < workdays) {
            result = result.plusDays(1);
            if (!(HolidayUtil.isHoliday(toDateStr2(result)))) {
                ++addedDays;
            }
        }

        return result;
    }

    /**
     * 指定日期减天数，并且跳过工作日
     *
     * @param date
     * @param workdays
     * @return
     */
    public static LocalDate minusWorkDay(LocalDate date, int workdays) {
        LocalDate result = date;
        int minusDays = 0;
        while (minusDays < workdays) {
            result = result.minusDays(1);
            if (!(HolidayUtil.isHoliday(toDateStr2(result)))) {
                ++minusDays;
            }
        }

        return result;
    }

    /**
     * 指定日期添加天数，并且跳过工作日
     *
     * @param date
     * @param workdays
     * @return
     */
    public static Date plusWorkDay(Date date, int workdays) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate localDate = instant.atZone(zoneId).toLocalDate();
        localDate = plusWorkDay(localDate, workdays);
        return Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
    }

    /**
     * 指定日期减天数，并且跳过工作日
     *
     * @param date
     * @param workdays
     * @return
     */
    public static Date minusWorkDay(Date date, int workdays) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate localDate = instant.atZone(zoneId).toLocalDate();
        localDate = minusWorkDay(localDate, workdays);
        return Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
    }

    /**
     * 指定开始日期到指定结束日期中在某个月份内的工时(只能获取当天之前的)
     *
     * @param startDate
     * @param endDate
     * @param month
     * @return
     */
    public static BigDecimal plusWorkHoursBetweenDate(Date startDate, Date endDate, int month) {
        BigDecimal result = BigDecimal.ZERO;
        LocalDate startLocalDate = null;
        LocalDate endLocalDate = null;
        String hourOfSpecialDay = "0";
        if (dateInMonth(startDate, month)) {
            // 请假开始当天或者结束当天,可能存在不满一天,计算当天工时
            hourOfSpecialDay = computeWorkHourOfDay(startDate, 0);
            Instant instant = startDate.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            startLocalDate = instant.atZone(zoneId).toLocalDate();
            endLocalDate = startLocalDate.withDayOfMonth(startLocalDate.lengthOfMonth());
            // 获取到当月最后一天,在加1天，避免开始时间为当月最后一天
            startLocalDate = startLocalDate.plusDays(1);
        }else if (dateInMonth(endDate, month)) {
            Instant instant = endDate.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            endLocalDate = instant.atZone(zoneId).toLocalDate();
            // 获取到第一天
            startLocalDate = LocalDate.of(DateUtil.year(endDate), month, 1);
        }else {
            return result;
        }
        // 统计前一天的请假工时
        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (!endLocalDate.isAfter(yesterday)) {
            hourOfSpecialDay = computeWorkHourOfDay(endDate, 1);
            // 如果 endDate <= 昨天的日期,则获取请假结束时间的前一天
            endLocalDate = endLocalDate.minusDays(1);
        } else {
            // 否则正常取当天的前一天
            endLocalDate = yesterday;
        }
        Integer completeDay = computeWorkDay(startLocalDate, endLocalDate);
        // 当天时间 + 完整工作日 * 7小时
        return result.add(new BigDecimal(hourOfSpecialDay)).add(new BigDecimal(completeDay ).multiply(new BigDecimal(HOUR_OF_WORKDAY)));
    }

    /**
     * 指定开始日期到指定结束日期中在某个月份内的工时
     *
     * @param startDate
     * @param endDate
     * @param month
     * @return
     */
    public static BigDecimal plusWorkHoursBetweenDateAll(Date startDate, Date endDate, int month) {
        BigDecimal result = BigDecimal.ZERO;
        LocalDate startLocalDate = null;
        LocalDate endLocalDate = null;
        String hourOfSpecialDay = "0";
        if (dateInMonth(startDate, month)) {
            // 请假开始当天或者结束当天,可能存在不满一天,计算当天工时
            hourOfSpecialDay = computeWorkHourOfDay(startDate, 0);
            Instant instant = startDate.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            startLocalDate = instant.atZone(zoneId).toLocalDate();
            endLocalDate = startLocalDate.withDayOfMonth(startLocalDate.lengthOfMonth());
            // 获取到当月最后一天,在加1天，避免开始时间为当月最后一天
            startLocalDate = startLocalDate.plusDays(1);
        }else if (dateInMonth(endDate, month)) {
            Instant instant = endDate.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            endLocalDate = instant.atZone(zoneId).toLocalDate();
            // 获取到第一天
            startLocalDate = LocalDate.of(DateUtil.year(endDate), month, 1);
        }else {
            return result;
        }
        Integer completeDay = computeWorkDay(startLocalDate, endLocalDate);
        // 当天时间 + 完整工作日 * 7小时
        return result.add(new BigDecimal(hourOfSpecialDay)).add(new BigDecimal(completeDay ).multiply(new BigDecimal(HOUR_OF_WORKDAY)));
    }

    /**
     * 计算开始时间到结束时间有多少个完整工作日 (包含结束当天)
     *
     * @param startDate 开始日期
     * @param endDate  结束日期
     * @return
     */
    public static Integer computeWorkDay(LocalDate startDate, LocalDate endDate) {
        int minusDays = 0;
        while (!startDate.isAfter(endDate) ) {
            if (!(HolidayUtil.isHoliday(toDateStr2(startDate)))) {
                ++minusDays;
            }
            startDate = startDate.plusDays(1);
        }

        return minusDays;
    }

    /**
     * 计算传入时间剩余当天工时
     *
     * @param date
     * @param type 0 :计算date到18点,1:计算9点到date
     * @return
     */
    public static String computeWorkHourOfDay(Date date, Integer type) {
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime inputTime = date.toInstant().atZone(zone).toLocalDateTime();
        LocalDate today = inputTime.toLocalDate();

        // 定义工作时间段
        LocalDateTime workStart = today.atTime(9, 0);
        LocalDateTime workEnd = today.atTime(18, 0);

        // 默认返回 7 小时整
        if (type == 0 && !inputTime.isAfter(workStart) && inputTime.isBefore(workEnd)) {
            return HOUR_OF_WORKDAY;
        }

        // 如果超过18:00，则根据type返回0或7
        if (inputTime.isAfter(workEnd) || inputTime.isEqual(workEnd)) {
            return type == 0 ? "0" : HOUR_OF_WORKDAY;
        }

        // 如果早于 9:00
        if (inputTime.isBefore(workStart)) {
            return type == 0 ? HOUR_OF_WORKDAY : "0";
        }

        Duration totalDuration;

        if (type == 0) {
            // 剩余时间：date -> 18:00
            totalDuration = Duration.between(inputTime, workEnd);
        } else {
            // 已用时间：9:00 -> date
            totalDuration = Duration.between(workStart, inputTime);
        }

        // 午休时间
        LocalDateTime lunchStart = today.atTime(12, 0);
        LocalDateTime lunchEnd = today.atTime(14, 0);

        // 计算与午休的重叠时间
        Duration overlap = getOverlap(
            type == 0 ? inputTime : workStart,
            type == 0 ? workEnd : inputTime,
            lunchStart, lunchEnd
        );

        long workHours = totalDuration.toHours() - overlap.toHours();
        return Long.toString(workHours);
    }

    /**
     * 获取两个时间段的重叠时间
     */
    private static Duration getOverlap(LocalDateTime start1, LocalDateTime end1,
                                       LocalDateTime start2, LocalDateTime end2) {
        LocalDateTime overlapStart = start1.isAfter(start2) ? start1 : start2;
        LocalDateTime overlapEnd = end1.isBefore(end2) ? end1 : end2;
        return overlapStart.isBefore(overlapEnd) ? Duration.between(overlapStart, overlapEnd) : Duration.ZERO;
    }

    private static Boolean dateInMonth(Date date, Integer month) {
        return DateUtil.month(date) + 1 == month;
    }


    public static Map<String, Map<String, BigDecimal>> computerWorkTime(List<TrackWorkResultVO> perfStatList, List<UserLeaveVo> userLeaveVos, Integer year, Integer month) {
        Map<String, Map<String, BigDecimal>> groupUserWorkTimes = new HashMap<>(32);
        for (TrackWorkResultVO perfStat : perfStatList) {
            String workGroup = perfStat.getWorkGroup();
            String userName = perfStat.getWorkUsername();
            String workMinutes = perfStat.getKqAttendanceWorkTime();
            // 获取用户的请假数据
            BigDecimal leaveHours = userLeaveVos.stream()
                .filter(leave -> leave.getNickName().equals(userName) && leave.getDurationTime() != null)
                .map(leave -> {
                    // 如果是同一个月的请假记录，则计算请假时长
                    if (compareTime(leave)) {
                        return TypeUtils.castToBigDecimal(leave.getDurationTime());
                    } else {
                        // 如果请假时间起始都不在本月
                        if (leaveMoreThanOneMonth(leave, year, month)) {
                            // 获取当月第一天 09:00
                            LocalDate localDate = LocalDate.of(year, month, 1);
                            LocalDateTime firstDayStart = localDate.atTime(9, 0);

                            // 获取当月最后一天 18:00
                            LocalDateTime lastDayEnd = localDate.withDayOfMonth(localDate.lengthOfMonth()).atTime(18, 0);
                            Date startDate = Date.from(firstDayStart.atZone(ZoneId.systemDefault()).toInstant());
                            Date endDate = Date.from(lastDayEnd.atZone(ZoneId.systemDefault()).toInstant());

                            return HolidayUtil.plusWorkHoursBetweenDate(startDate, endDate, month);
                        }
                        return HolidayUtil.plusWorkHoursBetweenDate(leave.getStartTime(), leave.getEndTime(), month);
                    }
                }).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算总工时
            BigDecimal totalWorkMinutes = new BigDecimal(workMinutes).add(leaveHours.multiply(new BigDecimal(60)));

            groupUserWorkTimes.computeIfAbsent(workGroup, k -> new HashMap<>(perfStatList.size())).put(userName, totalWorkMinutes);
        }
        return groupUserWorkTimes;
    }

     /**
      * <AUTHOR>
      * @date 2025/5/27 10:42
      * @Description 获取请假时长超过指定时间的人员
      * @param userLeaveVos
      * @param year
      * @param month
      */
    public static Map<String, BigDecimal>  computerTotalLeaveTime(List<UserLeaveVo> userLeaveVos, Integer year, Integer month) {
        Map<String, BigDecimal> nickNameToTotalHours = userLeaveVos.stream()
            .filter(leave -> leave.getNickName() != null && leave.getDurationTime() != null)
            .collect(Collectors.groupingBy(
                UserLeaveVo::getNickName,
                Collectors.mapping(
                    leave -> {
                        if (compareTime(leave)) {
                            return TypeUtils.castToBigDecimal(leave.getDurationTime());
                        } else if (leaveMoreThanOneMonth(leave, year, month)) {
                            // 获取当月第一天 09:00
                            LocalDate localDate = LocalDate.of(year, month, 1);
                            LocalDateTime firstDayStart = localDate.atTime(9, 0);

                            // 获取当月最后一天 18:00
                            LocalDateTime lastDayEnd = localDate.withDayOfMonth(localDate.lengthOfMonth()).atTime(18, 0);
                            Date startDate = Date.from(firstDayStart.atZone(ZoneId.systemDefault()).toInstant());
                            Date endDate = Date.from(lastDayEnd.atZone(ZoneId.systemDefault()).toInstant());

                            return HolidayUtil.plusWorkHoursBetweenDateAll(startDate, endDate, month);
                        } else {
                            return HolidayUtil.plusWorkHoursBetweenDateAll(leave.getStartTime(), leave.getEndTime(), month);
                        }
                    },
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                )
            ));
        return nickNameToTotalHours;
    }

    public static Set<String> getOverTimeLeaveNickName(List<UserLeaveVo> userLeaveVos, Integer year, Integer month) {
        if (CollectionUtil.isEmpty(userLeaveVos)) {
            return Collections.emptySet();
        }
        Map<String, BigDecimal> computerTotalLeaveTime = computerTotalLeaveTime(userLeaveVos, year, month);
        return computerTotalLeaveTime.entrySet().stream()
            .filter(entry -> entry.getValue().compareTo(new BigDecimal(OVER_HOUR_TIME)) >= 0)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }

    private static Boolean compareTime(UserLeaveVo userLeaveVo) {
        return DateUtil.month(userLeaveVo.getStartTime()) == DateUtil.month(userLeaveVo.getEndTime());
    }

    private static Boolean leaveMoreThanOneMonth(UserLeaveVo userLeaveVo, Integer year, Integer month) {
        Date leaveStart = userLeaveVo.getStartTime();
        Date leaveEnd = userLeaveVo.getEndTime();

        // 获取该年月第一天 和 最后一天
        Date firstDay = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + month + "-01"));
        Date lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + month + "-01"));

        // 判断是否跨整月
        return leaveStart.before(firstDay) && leaveEnd.after(lastDayOfMonth);
    }


    /**
     * 判断是否是有效工作日
     */
    public static boolean isValidWorkDay(String dateStr) {
        try {
            if (isLawHoliday(dateStr)) {
                return false;
            }
            if (isExtraWorkday(dateStr)) {
                return true;
            }
            return !isWeekends(dateStr);
        } catch (Exception e) {
            log.error("判断工作日异常", e);
            return false;
        }
    }

}

