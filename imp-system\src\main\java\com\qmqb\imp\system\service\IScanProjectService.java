package com.qmqb.imp.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.CodeBlockerStatisticsDTO;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.ScanProject;
import com.qmqb.imp.system.domain.bo.ScanProjectBo;
import com.qmqb.imp.system.domain.vo.ScanProjectVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 扫描项目记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface IScanProjectService {

    /**
     * 查询扫描项目记录
     * @param id 项目id
     * @return
     */
    ScanProjectVo queryById(Long id);

    /**
     * 查询扫描项目记录列表
     * @param bo 查询条件
     * @param pageQuery 分页查询条件
     * @return
     */
    TableDataInfo<ScanProjectVo> queryPageList(ScanProjectBo bo, PageQuery pageQuery);

    /**
     * 查询扫描项目记录列表
     * @param bo 查询条件
     * @return
     */
    List<ScanProjectVo> queryList(ScanProjectBo bo);

    /**
     * 新增扫描项目记录
     * @param bo 新增项目记录
     * @return
     */
    Boolean insertByBo(ScanProjectBo bo);

    /**
     * 新增扫描项目记录
     *
     * @param scanProject 新增项目记录
     * @return
     */
    Boolean insert(ScanProject scanProject);

    /**
     * 修改扫描项目记录
     * @param bo 修改项目记录
     * @return
     */
    Boolean updateByBo(ScanProjectBo bo);

    /**
     * 校验并批量删除扫描项目记录信息
     * @param ids 删除项目id
     * @param isValid 是否有效
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过项目id批量删除记录
     *
     * @param deleteIds 删除项目id
     * @return
     */
    Boolean batchDeleteByIds(Collection<Long> deleteIds);

    /**
     * 批量修改项目开发部门
     *
     * @param devDeptInPid 部门id
     * @return
     */
    int updateDevDeptBatch(Map<Long, String> devDeptInPid);

    /**
     * 修改旧的扫描记录
     *
     * @param id 项目id
     * @return
     */
    int updatePreviousScanToOld(Long id);

    /**
     * 分页查询
     *
     * @param scanProjectBo 查询条件
     * @param page 分页查询条件
     * @return
     */
    Page<ScanProject> pageDeleteList(ScanProjectBo scanProjectBo, Page page);

    /**
     * 严重错误统计列表
     *
     * @param year  年份
     * @param month 月份
     * @return
     */
    List<CodeBlockerStatisticsDTO> blockerStatisticsList(Integer year, Integer month);

    /**
     * 批量软删除扫描项目记录
     *
     * @param projectIds 项目ID集合
     * @return 是否成功
     */
    Boolean batchSoftDeleteByProjectIds(Set<Long> projectIds);

}
