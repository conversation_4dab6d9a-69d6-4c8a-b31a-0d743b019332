package com.qmqb.imp.job.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.UserLeaveTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.client.DingTalkApiClient;
import com.qmqb.imp.system.domain.UserLeave;
import com.qmqb.imp.system.mapper.UserLeaveMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.ISysUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableObject;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动同步请假时长数据
 *
 * <AUTHOR>
 * @since 2024/12/4 16:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncUserLeaveDataService {
    private final UserLeaveMapper userLeaveMapper;
    private final DingTalkApiClient dingTalkApiClient;
    private final ISysUserService iSysUserService;

    private static final String PROCESS_CODE = "PROC-DD37ACD0-2B9B-4E23-A5AE-3D9F13E76A28";
    private static final long ONE_DAY_MILLIS = 86400000;

    @TraceId("自动同步请假时长数据定时任务")
    @XxlJob("syncUserLeaveDataServiceJobHandler")
    public ReturnT<String> syncUserLeaveDataServiceJobHandler(String dayAgo) {
        if (StringUtils.isBlank(dayAgo)) {
            dayAgo = "2";
        }
        try {
            XxlJobLogger.log("开始执行自动同步请假时长数据定时任务定时任务...");
            log.info("开始执行自动同步请假时长数据定时任务定时任务...");
            val sw = new StopWatch();
            sw.start();
            val now = System.currentTimeMillis();
            val processInstanceIds = dingTalkApiClient.listProcessInstanceIds(PROCESS_CODE, now - ONE_DAY_MILLIS * Integer.parseInt(dayAgo), now, 0L, Collections.emptyList());
            log.info("processInstanceIds:{}", processInstanceIds);
            List<UserLeave> batchInsertArgs = processInstanceIds.stream().map(id -> {
                val pi = dingTalkApiClient.getProcessInstance(id);
                val userId = userLeaveMapper.selectUserIdByDingtalk(pi.originatorUserId);
                if (userId == null) {
                    return null;
                }
                SysUser user = iSysUserService.selectUserById(userId);
                val deptId = user.getDeptId();
                SysDept dept = user.getDept();
                val nickName = user.getNickName();
                val deptName = dept.getDeptName();
                val startTime = new MutableObject<>(new Date(0));
                val endTime = new MutableObject<>(new Date(0));
                val duration = new MutableObject<>("");
                val leaveType = new MutableObject<>("");
                pi.formComponentValues.stream().filter(e -> "DDHolidayField".equals(e.componentType)).findAny().ifPresent(e -> {
                        val value = JSON.parseArray(e.value);
                        startTime.setValue(value.getDate(0));
                        endTime.setValue(value.getDate(1));
                        if (startTime.equals(endTime)) {
                            endTime.setValue(DateUtil.endOfDay(endTime.getValue()));
                        }
                        val extValue = JSON.parseObject(e.getExtValue());
                        duration.setValue("DAY".equals(extValue.getString("unit")) ? getActualDuration(extValue) : extValue.getString("durationInHour"));
                        val extension = JSON.parseObject(extValue.getString("extension"));
                        UserLeaveTypeEnum tag = UserLeaveTypeEnum.getByDescription(extension.getString("tag"));
                        if (tag != null) {
                            leaveType.setValue(tag.getCode());
                        }
                    });
                val leader = Optional.ofNullable(dept.getLeader()).filter(StringUtils::isNotBlank).map(e -> e.split(",")[0])
                    .flatMap(e -> iSysUserService.selectUserByNickNames(Collections.singletonList(e)).stream().findFirst()).map(SysUser::getUserName).orElse(dept.getLeader());
                val applyTime = TypeUtils.castToDate(pi.createTime);
                val passTime = TypeUtils.castToDate(pi.finishTime);
                val status = "agree".equals(pi.result) && "COMPLETED".equals(pi.status) ? (!"REVOKE".equals(pi.bizAction)? 1 : 2): 0;
                val dingTalkNo = pi.businessId;
                UserLeave userLeave = new UserLeave();
                userLeave.setUserId(userId);
                userLeave.setNickName(nickName);
                userLeave.setDeptId(deptId);
                userLeave.setDeptName(deptName);
                userLeave.setStartTime(startTime.getValue());
                userLeave.setEndTime(endTime.getValue());
                userLeave.setLeader(leader);
                userLeave.setApplyTime(applyTime);
                userLeave.setPassTime(passTime);
                userLeave.setDurationTime(duration.getValue());
                userLeave.setLeaveType(leaveType.getValue());
                userLeave.setStatus(status);
                userLeave.setDingTalkNo(dingTalkNo);
                return userLeave;
            }).filter(Objects::nonNull).collect(Collectors.collectingAndThen(Collectors.groupingBy(UserLeave::getDingTalkNo, LinkedHashMap::new, Collectors.toList()),
                map -> map.values().stream().map(list -> { return list.stream().filter(u -> u.getStatus() == 2).findFirst().orElse(list.get(0)); }).collect(Collectors.toList())
            ));
            if (CollectionUtils.isNotEmpty(batchInsertArgs)) {
                userLeaveMapper.batchInsertOnUpdate(batchInsertArgs);
            }
            sw.stop();
            XxlJobLogger.log("自动同步请假时长数据定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("自动同步请假时长数据定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("自动同步请假时长数据定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    private String getActualDuration(JSONObject extValue) {
        String key = "detailList";
        Integer workday = 0;
        for (Object detail : extValue.getJSONArray(key)) {
            JSONObject detailObj = (JSONObject) detail;
            if (!HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date(detailObj.getLong("workDate"))))) {
                workday++;
            }
        }
        return String.valueOf(workday * 7);
    }
}
