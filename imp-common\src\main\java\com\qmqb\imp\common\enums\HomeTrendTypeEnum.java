package com.qmqb.imp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HomeTrendTypeEnum {

    DEVELOPER(1, "开发"),
    TESTER(2, "测试"),
    PM(3, "项管"),
    LEADER(4, "组长"),
    TASKWAIT(5, "等待任务"),
    TASKDOING(6, "进行中任务"),
    TASKDONE(7, "已完成任务"),
    TASKPAUSE(8, "暂停任务"),
    DOCCOUNT(9, "文档数"),
    GITPROJECTCOUNT(10, "代码库数"),
    TASKCLOSED(11, "关闭任务"),
    TASKCANCEL(12, "取消任务"),
    ;

    private Integer code;
    private String desc;

    public static HomeTrendTypeEnum getEnumByCode(Integer code) {
        for (HomeTrendTypeEnum homeTrendType : HomeTrendTypeEnum.values()) {
            if (homeTrendType.getCode().equals(code)) {
                return homeTrendType;
            }
        }
        return null;
    }
}
