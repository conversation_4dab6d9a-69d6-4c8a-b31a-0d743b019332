package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】视图对象 zt_project
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class ZtProjectVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer project;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String model;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String type;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String product;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String lifetime;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String budget;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String budgetUnit;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String attribute;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String percent;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String milestone;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String output;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String auth;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer parent;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String path;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer grade;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String name;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String code;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date begin;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date end;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date realBegan;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date realEnd;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer days;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String status;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String subStatus;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String statge;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String pri;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String desc;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer parentVersion;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long planDuration;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long realDuration;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String openedBy;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date openedDate;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String openedVersion;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String lastEditedBy;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date lastEditedDate;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String closedBy;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date closedDate;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String canceledBy;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date canceledDate;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String PO;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String PM;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String QD;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String RD;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String team;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String acl;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String whitelist;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer order;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String deleted;


}
