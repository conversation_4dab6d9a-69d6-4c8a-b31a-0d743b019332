package com.qmqb.imp.common.enums;

import com.qmqb.imp.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/1/3
 */

@Getter
@AllArgsConstructor
public enum ZtTaskStatusEnum {

    WAIT("wait", "未开始"),
    DOING("doing", "进行中"),
    DONE("done", "已完成"),
    PAUSE("pause", "暂停"),
    CANCEL("cancel", "取消"),
    CLOSED("closed", "关闭");

    private String value;
    private String desc;

    public static String getDescByValue(String value) {
        for (ZtTaskStatusEnum taskStatusEnum : ZtTaskStatusEnum.values()) {
            if (StringUtils.equals(value, taskStatusEnum.getValue())) {
                return taskStatusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }


}
