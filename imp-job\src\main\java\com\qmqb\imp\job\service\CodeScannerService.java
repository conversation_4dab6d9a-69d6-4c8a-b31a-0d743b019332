package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.job.api.client.GitlabApiClient;
import com.qmqb.imp.job.api.config.GitlabConfig;
import com.qmqb.imp.system.domain.ScanProject;
import com.qmqb.imp.system.domain.ScanProjectDetail;
import com.qmqb.imp.system.domain.ScanProjectFile;
import com.qmqb.imp.system.domain.bo.ScanProjectBo;
import com.qmqb.imp.system.domain.vo.ProjectVo;
import com.qmqb.imp.system.domain.vo.ScanProjectVo;
import com.qmqb.imp.system.service.IProjectService;
import com.qmqb.imp.system.service.IScanProjectDetailService;
import com.qmqb.imp.system.service.IScanProjectFileService;
import com.qmqb.imp.system.service.IScanProjectService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.LogCommand;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.filter.CommitTimeRevFilter;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.treewalk.TreeWalk;
import org.eclipse.jgit.treewalk.filter.PathFilter;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.Branch;
import org.gitlab4j.api.models.Commit;
import org.gitlab4j.api.models.Project;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.annotation.PostConstruct;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 代码扫描定时器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CodeScannerService {

    private final IScanProjectService scanProjectService;
    private final IScanProjectDetailService scanProjectDetailService;
    private final IScanProjectFileService scanProjectFileService;
    private final GitlabApiClient gitlabApiClient;
    private final GitlabConfig gitlabConfig;
    private final IProjectService iProjectService;
    @Value("${p3c.directory:/www/p3c}")
    private String p3cDir;
    @Value("${p3c.branch:master}")
    private String branch;
    private final static String CODE_FILENAME = "codes";
    private final static String INCREMENT_FOLDER = "incrementFolder";
    private File p3cJarFile;
    private List<File> p3cJavaRuleFiles = new ArrayList<>();

    @PostConstruct
    public void init() {
        if (StrUtil.isBlank(p3cDir)) {
            p3cDir = "/www/p3c";
        }
        if (StrUtil.isBlank(branch)) {
            branch = "master";
        }
        p3cJarFile = new File(p3cDir.concat(File.separator).concat("p3c-pmd.jar"));
        p3cJavaRuleFiles = Lists.newArrayList(
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-comment.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-concurrent.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-constant.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-exception.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-flowcontrol.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-naming.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-oop.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-other.xml")),
            new File(p3cDir.concat(File.separator).concat("rulesets/java/ali-set.xml"))
        );
    }

    /**
     * 代码扫描定时任务
     */
    @TraceId("代码扫描定时任务")
    @XxlJob("codeScannerJobHandler")
    public ReturnT<String> codeScannerJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行代码扫描定时任务...");
            log.info("开始执行代码扫描定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            //查询gitlab上所有项目
            List<Project> projects;
            if (StrUtil.isNotBlank(param)) {
                projects = new ArrayList<>();
                projects.add(gitlabApiClient.build().getProjectApi().getProject(Long.valueOf(param)));
            } else {
                projects = gitlabApiClient.build().getProjectApi().getProjects();
            }
            if (CollUtil.isEmpty(projects)) {
                throw new RuntimeException("未获取到任何项目");
            }
            //判断p3c扫描jar包和规则文件是否存在
            boolean ruleFileExists = true;
            for (File p3cJavaRuleFile : p3cJavaRuleFiles) {
                if (!(ruleFileExists = p3cJavaRuleFile.exists())) {
                    break;
                }
            }
            if (!p3cJarFile.exists() || !ruleFileExists) {
                log.error("缺失必要文件，请检查后面这些文件是否存在；jar扫描文件：{},规则文件：{}", p3cJarFile.getAbsolutePath(), p3cJavaRuleFiles);
                throw new RuntimeException("获取不到p3c扫描所需文件，请检查相关目录");
            }
            projects=projects.stream().filter(project -> !project.getArchived()).collect(Collectors.toList());
            List<Long> allProjectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
            Map<Long, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, v -> v, (v1, v2) -> v2));
            List<ProjectVo> projectVoList = iProjectService.getByProjects();
            Map<Long, ProjectVo> projectVoMap = projectVoList.stream().collect(Collectors.toMap(ProjectVo::getPId, v -> v, (v1, v2) -> v2));
            //查询所有有扫描记录的项目
            ScanProjectBo scanProjectBo = new ScanProjectBo();
            scanProjectBo.setLastScanFlag(CommConstants.CommonVal.ONE);
            List<ScanProjectVo> scanProjectVos = scanProjectService.queryList(scanProjectBo);
            Map<Long, ScanProjectVo> scanProjectVoMap = scanProjectVos.stream().collect(Collectors.toMap(ScanProjectVo::getPId, v -> v, (v1, v2) -> v2));
            List<Long> scanProjectIds = scanProjectVos.stream().map(ScanProjectVo::getPId).collect(Collectors.toList());
            //全量扫描
            Set<Long> fullScanProjectIds = new HashSet<>();
            //增量扫描
            Set<Long> incrementScanProjectIds = new HashSet<>();
            for (Long projectId : allProjectIds) {
                if (scanProjectIds.contains(projectId)) {
                    incrementScanProjectIds.add(projectId);
                } else {
                    fullScanProjectIds.add(projectId);
                }
            }

            //执行全量扫描
            fullScanProjects(fullScanProjectIds, projectMap, projectVoMap);

            //执行增量扫描
            incrementScanProjects(incrementScanProjectIds, scanProjectVoMap, projectMap, projectVoMap);

            //删除不存在的代码库记录
            CodeScannerService codeScannerService = ((CodeScannerService) AopContext.currentProxy());
            codeScannerService.delteScanProject(projectVoList);

            //删除40天外项目的扫描结果
            SpringUtil.getBean(CodeScannerService.class).deleteProjectsOutside40Days(projects);

            sw.stop();
            XxlJobLogger.log("代码扫描定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("代码扫描定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("代码扫描定时任务执行异常：", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 全量扫描
     *
     * @param fullScanProjectIds
     * @param projectMap
     * @param projectVoMap
     */
    public void fullScanProjects(Set<Long> fullScanProjectIds, Map<Long, Project> projectMap, Map<Long, ProjectVo> projectVoMap) {
        CodeScannerService codeScannerService = ((CodeScannerService) AopContext.currentProxy());
        //执行全量扫描
        //--------------------------------------------------------------------
        for (Long projectId : fullScanProjectIds) {
            try {
                Project project = projectMap.get(projectId);
                ProjectVo projectVo = projectVoMap.get(projectId);
                Boolean branchIsExist = this.branchIsExist(project.getId(), branch);
                // 添加40天内提交时间的判断
                boolean isWithin40Days = this.isProjectCommittedWithin40Days(project.getId(), branch);
                boolean judgeBeforeScan = ObjUtil.isAllNotEmpty(project, projectVo) && branchIsExist && isWithin40Days && (project.getDescription() == null || !project.getDescription().contains("废弃"));
                if (judgeBeforeScan) {
                    //拉取项目代码
                    String codePath = this.cloneOrPullRepository(project, branch, gitlabConfig.getToken());
                    Date nowScanTime = new Date();
                    String result = this.runP3cScan(true, projectId, codePath, null);
                    Date lastCommitTime = this.getLastCommitTime(codePath);
                    codeScannerService.processScanResults(project, projectVo, branch, result, true, nowScanTime, lastCommitTime, null, null);
                } else if (!isWithin40Days) {
                    log.info("项目[{}]最后提交时间不在40天内，跳过全量扫描", projectId);
                }
            } catch (Exception e) {
                log.error("全量扫描项目[{}]时发生异常，跳过该项目继续处理其他项目：", projectId, e);
                XxlJobLogger.log("全量扫描项目[{}]失败：{}", projectId, e.getMessage());
            }
        }
    }

    /**
     * 执行增量扫描
     *
     * @param incrementScanProjectIds
     * @param scanProjectVoMap
     * @param projectMap
     * @param projectVoMap
     */
    public void incrementScanProjects(Set<Long> incrementScanProjectIds, Map<Long, ScanProjectVo> scanProjectVoMap, Map<Long, Project> projectMap, Map<Long, ProjectVo> projectVoMap) {
        CodeScannerService codeScannerService = ((CodeScannerService) AopContext.currentProxy());
        for (Long projectId : incrementScanProjectIds) {
            try {
                ScanProjectVo scanProjectVo = scanProjectVoMap.get(projectId);
                Project project = projectMap.get(projectId);
                ProjectVo projectVo = projectVoMap.get(projectId);
                Boolean branchIsExist = this.branchIsExist(project.getId(), branch);
                // 添加40天内提交时间的判断
                boolean isWithin40Days = this.isProjectCommittedWithin40Days(project.getId(), branch);
                boolean judgeBeforeScan = ObjUtil.isAllNotEmpty(scanProjectVo, project, projectVo) && branchIsExist && isWithin40Days && (project.getDescription() == null || !project.getDescription().contains("废弃"));
                if (judgeBeforeScan) {
                    //拉取项目代码
                    String codePath = this.cloneOrPullRepository(project, branch, gitlabConfig.getToken());
                    Date nowScanTime = new Date();
                    Date lastScanTime = scanProjectVo.getLastScanTime();
                    Set<String> incrementFiles = this.getIncrementFiles(codePath, lastScanTime, nowScanTime);
                    String result = this.runP3cScan(false, projectId, codePath, incrementFiles);
                    Date lastCommitTime = this.getLastCommitTime(codePath);
                    codeScannerService.processScanResults(project, projectVo, branch, result, false, nowScanTime, lastCommitTime, scanProjectVo, incrementFiles);
                } else if (!isWithin40Days) {
                    log.info("项目[{}]最后提交时间不在40天内，跳过增量扫描", projectId);
                }
            } catch (Exception e) {
                log.error("增量扫描项目[{}]时发生异常，跳过该项目继续处理其他项目：", projectId, e);
                XxlJobLogger.log("增量扫描项目[{}]失败：{}", projectId, e.getMessage());
            }
        }
    }

    /**
     * 判断分支是否存在
     *
     * @param id
     * @param branch
     * @return
     */
    private Boolean branchIsExist(Long id, String branch) {
        try {
            log.info("开始判断分支是否存在,项目id:{},分支名称:{}", id, branch);
            List<Branch> branches = gitlabApiClient.build().getRepositoryApi().getBranches(id);
            if (CollUtil.isNotEmpty(branches)) {
                for (Branch b : branches) {
                    if (b.getName().equals(branch)) {
                        return true;
                    }
                }
            }
        } catch (GitLabApiException e) {
            throw new RuntimeException("开始判断分支是否存在异常;", e);
        }
        return false;
    }

    /**
     * 计算40天前的日期
     *
     * @return 40天前的日期
     */
    private Date getFortyDaysAgoDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -40);
        return calendar.getTime();
    }

    /**
     * 判断项目是否在40天内有提交记录（优化版本，直接查询40天内的提交）
     *
     * @param projectId 项目ID
     * @param branch 分支名称
     * @return true表示在40天内有提交，false表示没有或获取失败
     */
    private boolean isProjectCommittedWithin40Days(Long projectId, String branch) {
        try {
            Date fortyDaysAgo = getFortyDaysAgoDate();
            log.info("开始检查项目[{}]分支[{}]是否在40天内有提交记录，40天前时间: {}", projectId, branch, fortyDaysAgo);

            // 直接查询40天内的提交记录，限制返回1条即可
            List<Commit> commits = gitlabApiClient.build().getCommitsApi()
                .getCommits(projectId, branch, fortyDaysAgo, null, 1).all();

            boolean hasCommitsWithin40Days = CollUtil.isNotEmpty(commits);
            if (hasCommitsWithin40Days) {
                Date lastCommitTime = commits.get(0).getCommittedDate();
                log.info("项目[{}]分支[{}]在40天内有提交记录，最新提交时间: {}", projectId, branch, lastCommitTime);
            } else {
                log.info("项目[{}]分支[{}]在40天内没有提交记录", projectId, branch);
            }

            return hasCommitsWithin40Days;
        } catch (GitLabApiException e) {
            log.error("检查项目[{}]分支[{}]40天内提交记录时发生异常: ", projectId, branch, e);
            return false;
        }
    }



    /**
     * 自动检测字符编码
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static String detectCharset(InputStream inputStream) throws IOException {
        int byteSize = 4096;
        // 限制读取的字节数为 4KB
        byte[] buf = new byte[byteSize];
        UniversalDetector detector = new UniversalDetector(null);

        int nread;
        int totalRead = 0;
        while ((nread = inputStream.read(buf)) > 0 && !detector.isDone() && totalRead < byteSize) {
            detector.handleData(buf, 0, nread);
            totalRead += nread;
        }
        detector.dataEnd();

        String encoding = detector.getDetectedCharset();
        detector.reset();
        return encoding;
    }

    /**
     * 克隆、更新代码库
     *
     * @param project      项目
     * @param branch       分支
     * @param privateToken git token
     * @return
     */
    public String cloneOrPullRepository(Project project, String branch, String privateToken) {
        Git git = null;
        try {
            if (StrUtil.isBlank(branch)) {
                branch = project.getDefaultBranch();
            }
            String cloneUrl = project.getHttpUrlToRepo().replace("https://", "https://oauth2:" + gitlabApiClient.build().getAuthToken() + "@");

            Path projectDir = Paths.get(p3cDir, CODE_FILENAME, String.valueOf(project.getId()), branch);
            File gitFile = new File(projectDir.toFile().getAbsolutePath().concat("/.git"));
            CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider("PRIVATE-TOKEN", privateToken);
            try {
                if (gitFile.exists()) {
                    log.info("项目目录[" + projectDir + "]已经存在,开始git pull操作。");
                    git = Git.open(projectDir.toFile());
                    git.pull().setCredentialsProvider(credentialsProvider).call();
                } else {
                    log.info("项目目录[" + projectDir + "]不存在,开始git clone操作。");
                    git = Git.cloneRepository().setCredentialsProvider(credentialsProvider).setURI(cloneUrl).setBranch(branch)
                        .setDirectory(projectDir.toFile()).call();

                }
            } catch (GitAPIException e) {
                throw new RuntimeException("Git Api操作失败: " + e.getMessage(), e);
            }

            log.info("gitlab代码克隆、更新已完成,项目目录为[" + projectDir + "]");
            return projectDir.toString();
        } catch (IOException e) {
            throw new RuntimeException("IO 错误: " + e.getMessage(), e);
        } finally {
            if (git != null) {
                git.close();
            }
        }
    }

    /**
     * 用p3c扫描代码
     *
     * @param projectId      项目id
     * @param isFull         是否为全量扫描
     * @param codePath       代码磁盘路径
     * @param incrementFiles 增量文件
     * @return
     */
    public String runP3cScan(Boolean isFull, Long projectId, String codePath, Set<String> incrementFiles) {
        try {
            log.info("正在扫描项目，项目id: {}", projectId);
            // 增量扫描路径处理
            codePath = updateCodePath(isFull, projectId, codePath, incrementFiles);
            if (StrUtil.isBlank(codePath)) {
                return null;
            }
            String p3cJavaRuleFilesPath = p3cJavaRuleFiles.stream()
                .map(File::getAbsolutePath)
                .collect(Collectors.joining(","));
            Process process = new ProcessBuilder(
                "java", "-cp", p3cJarFile.getAbsolutePath(),
                "net.sourceforge.pmd.PMD",
                "-d", codePath,
                "-f", "xml",
                "-R", p3cJavaRuleFilesPath
            ).redirectErrorStream(true).start();
            // 异步等待进程结束
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 缓存子进程的输出
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = process.getInputStream().read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    byte[] outputBytes = outputStream.toByteArray();
                    // 检测字符编码
                    String detectedCharset = detectCharset(new ByteArrayInputStream(outputBytes));
                    if (StrUtil.isBlank(detectedCharset)) {
                        detectedCharset = "GBK"; // 默认使用 GBK 编码
                    }
                    // 使用缓存的数据创建 BufferedReader
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(outputBytes), Charset.forName(detectedCharset)))) {
                        StringBuilder output = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            log.info(line);
                            output.append(line).append("\n");
                        }
                        return output.toString();
                    }
                } catch (IOException e) {
                    log.error("读取进程输出时发生异常", e);
                    throw new RuntimeException(e);
                }
            });
            // 设置超时时间
            try {
                return future.get(30, TimeUnit.MINUTES);
            } catch (TimeoutException | ExecutionException e) {
                log.error("扫描进程异常", e);
                throw new RuntimeException("扫描进程异常", e);
            } catch (InterruptedException e) {
                log.error("扫描进程被中断", e);
                Thread.currentThread().interrupt(); // 重新设置中断状态
                throw new RuntimeException("扫描进程被中断", e);
            } finally {
                if (process != null) {
                    process.destroy();
                    try {
                        process.waitFor();
                    } catch (InterruptedException e) {
                        log.warn("等待进程结束时被中断", e);
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } catch (IOException e) {
            log.error("创建进程时发生IO异常", e);
            throw new RuntimeException("创建进程时发生IO异常", e);
        } finally {
            if (!isFull && StrUtil.isNotBlank(codePath)) {
                // 删除临时文件
                Path targetPath = Paths.get(codePath).getParent().resolve(INCREMENT_FOLDER);
                FileUtil.del(targetPath);
            }
        }
    }

    /**
     * 修改增量扫描路径
     *
     * @param isFull
     * @param projectId
     * @param codePath
     * @param incrementFiles
     * @return
     * @throws IOException
     */
    public String updateCodePath(Boolean isFull, Long projectId, String codePath, Set<String> incrementFiles) throws IOException {
        // 增量扫描路径处理
        if (!isFull) {
            if (CollUtil.isEmpty(incrementFiles)) {
                log.info("没有找到需要扫描的文件，项目id: {}", projectId);
                return null;
            }
            // 增量文件拷贝到一个临时文件夹，扫描完后删掉
            Path targetPath = Paths.get(codePath).getParent().resolve(INCREMENT_FOLDER);
            if (Files.notExists(targetPath)) {
                Files.createDirectories(targetPath);
            }
            for (String incrementFile : incrementFiles) {
                Path sourcePath = Paths.get(codePath).resolve(incrementFile);
                //可能是gitlab删除文件操作，所以需要判断源文件是否存在
                if (Files.exists(sourcePath)) {
                    Path targetFilePath = targetPath.resolve(incrementFile);
                    FileUtil.copyFilesFromDir(sourcePath.toFile(), targetFilePath.toFile(), true);
                }
            }
            codePath = targetPath.toAbsolutePath().toString();
        }
        return codePath;
    }

    /**
     * 获取指定时间范围修改过的文件集合
     *
     * @return
     */
    public Set<String> getIncrementFiles(String repoPath, Date startDate, Date endDate) {
        Set<String> modifiedFiles = new HashSet<>();
        Git git = null;
        try (Repository repository = new FileRepositoryBuilder()
            .setGitDir(new File(repoPath.concat(File.separator).concat(".git")))
            .readEnvironment()
            .findGitDir()
            .build()) {

            git = new Git(repository);
            LogCommand logCommand = git.log();
            // 设置时间范围过滤器
            logCommand.setRevFilter(CommitTimeRevFilter.between(startDate.getTime(), endDate.getTime()));
            Iterable<RevCommit> commits = logCommand.call();
            for (RevCommit commit : commits) {
                TreeWalk treeWalk = new TreeWalk(repository);
                treeWalk.addTree(commit.getTree());
                if (commit.getParentCount() > 0) {
                    treeWalk.addTree(commit.getParent(0).getTree());
                } else {
                    // 处理初始提交，没有父提交
                    treeWalk.addTree(commit.getTree());
                }
                treeWalk.setFilter(PathFilter.ANY_DIFF);
                treeWalk.setRecursive(true);
                while (treeWalk.next()) {
                    modifiedFiles.add(treeWalk.getPathString());
                }
            }
        } catch (IOException | GitAPIException e) {
            throw new RuntimeException(e);
        } finally {
            if (git != null) {
                git.close();
            }
        }
        return modifiedFiles;
    }

    /**
     * 获取最后一次提交代码的时间
     *
     * @param codePath
     * @return
     */
    private Date getLastCommitTime(String codePath) {
        Path projectDir = Paths.get(codePath);
        File repoDir = new File(projectDir.toFile().getAbsolutePath().concat("/.git"));
        try (Repository repository = new FileRepositoryBuilder()
            .setGitDir(repoDir)
            .build()) {
            try (Git git = new Git(repository)) {

                // 获取 HEAD 的引用
                RevCommit commit = git.log().call().iterator().next();

                // 获取提交时间
                long commitTime = commit.getCommitTime();
                Date date = new Date(commitTime * 1000L);

                return date;
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 解析扫描结果，并存到数据库中
     *
     * @param project        项目
     * @param projectVo      项目信息
     * @param branch         扫描分支
     * @param result         扫描结果
     * @param isFull         是否全量扫描
     * @param nowScanTime    当前扫描时间
     * @param scanProjectVo  上一次扫描记录
     * @param incrementFiles 增量文件集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void processScanResults(Project project, ProjectVo projectVo, String branch, String result, Boolean isFull, Date nowScanTime, Date lastCommitTime, ScanProjectVo scanProjectVo, Set<String> incrementFiles) {
        if (StrUtil.isEmpty(result)) {
            log.info("没有可用的扫描结果");
            return;
        }
        int separatorIndex = result.indexOf("<?xml");
        if (separatorIndex != -1) {
            result = result.substring(separatorIndex);
        }
        //非法xml处理
        int errorIndex = result.indexOf("<error");
        if (errorIndex != -1) {
            List<String> fileBlocks = this.extractFileBlocks(result);
            result = "<xml>".concat(String.join("", fileBlocks)).concat("</xml>");
        }
        Path projectDir = Paths.get(p3cDir, CODE_FILENAME, String.valueOf(project.getId()), branch);
        List<String> scanFileUrls = new ArrayList<>();
        //增量文件处理
        if (CollUtil.isNotEmpty(incrementFiles)) {
            for (String incrementFile : incrementFiles) {
                String deleteFileUrl = projectDir.resolve(incrementFile).toFile().getAbsolutePath().replace(projectDir.toFile().getAbsolutePath().concat(File.separator), "");
                scanFileUrls.add(deleteFileUrl);
            }
        }
        log.info("\n代码扫描结果开始处理并入库");
        log.info("-------------------------------------------------------------------------------");
        try {
            List<ScanProjectDetail> scanProjectDetailList = new ArrayList<>();
            ScanProjectDetail scanProjectDetail;
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(result.getBytes()));
            NodeList fileNodes = document.getElementsByTagName("file");
            for (int i = 0; i < fileNodes.getLength(); i++) {
                Element fileElement = (Element) fileNodes.item(i);
                String scanFileUrl;
                if (isFull) {
                    scanFileUrl = fileElement.getAttribute("name").replace(projectDir.toFile().getAbsolutePath().concat(File.separator), "");
                } else {
                    scanFileUrl = fileElement.getAttribute("name").replace(projectDir.getParent().resolve(INCREMENT_FOLDER).toFile().getAbsolutePath().concat(File.separator), "");
                }
                scanFileUrls.add(scanFileUrl);
                NodeList violationNodes = fileElement.getElementsByTagName("violation");
                for (int j = 0; j < violationNodes.getLength(); j++) {
                    Element violationElement = (Element) violationNodes.item(j);
                    scanProjectDetail = new ScanProjectDetail();
                    scanProjectDetail.setPId(project.getId());
                    scanProjectDetail.setIsHandle(CommConstants.CommonVal.ZERO);
                    scanProjectDetail.setScanFileUrl(scanFileUrl.replaceAll("\\\\", "/"));
                    scanProjectDetail.setScanBeginLine(Long.valueOf(violationElement.getAttribute("beginline")));
                    scanProjectDetail.setScanEndLine(Long.valueOf(violationElement.getAttribute("endline")));
                    Long begincolumn = Long.valueOf(violationElement.getAttribute("begincolumn"));
                    Long endcolumn = Long.valueOf(violationElement.getAttribute("endcolumn"));
                    scanProjectDetail.setScanBeginColumn(begincolumn);
                    scanProjectDetail.setScanEndColumn(endcolumn < begincolumn ? begincolumn : endcolumn);
                    scanProjectDetail.setScanRule(violationElement.getAttribute("rule"));
                    scanProjectDetail.setScanRuleSet(violationElement.getAttribute("ruleset"));
                    scanProjectDetail.setScanClass(violationElement.getAttribute("class"));
                    Long scanPriority = Long.valueOf(violationElement.getAttribute("priority"));
                    scanProjectDetail.setScanPriority(scanPriority);
                    scanProjectDetail.setScanMethod(violationElement.getAttribute("method"));
                    scanProjectDetail.setScanVariable(violationElement.getAttribute("variable"));
                    scanProjectDetail.setScanPackage(violationElement.getAttribute("package"));
                    scanProjectDetail.setScanDescribe(StrUtil.removeAllLineBreaks(violationElement.getTextContent()));

                    // 过滤轻微问题（priority=3），不入库
                    if (!scanPriority.equals((long) CommConstants.CommonVal.THREE)) {
                        scanProjectDetailList.add(scanProjectDetail);
                    }
                }
            }
            operateData(isFull, scanFileUrls, projectVo, project, scanProjectDetailList, nowScanTime, lastCommitTime, scanProjectVo);
        } catch (ParserConfigurationException | SAXException | IOException e) {
            log.error("处理扫描结果时发生异常", e);
            throw new RuntimeException("扫描结果解析异常", e);
        } catch (Exception e) {
            log.error("处理扫描结果时发生未知异常", e);
            throw new RuntimeException("处理扫描结果时发生未知异常", e);
        }
    }

    /**
     * 操作数据
     *
     * @param isFull                是否全量扫描
     * @param scanFileUrls          扫描文件路径集合
     * @param projectVo             项目信息
     * @param project               gitlab项目信息
     * @param scanProjectDetailList 扫描详情列表
     * @param nowScanTime           当前扫描时间
     * @param lastCommitTime        最后代码提交时间
     * @param scanProjectVo         上一次扫描记录
     */
    public void operateData(Boolean isFull, List<String> scanFileUrls, ProjectVo projectVo, Project project, List<ScanProjectDetail> scanProjectDetailList, Date nowScanTime, Date lastCommitTime, ScanProjectVo scanProjectVo) {
        Long scanVersion = (!isFull && scanProjectVo != null) ? (scanProjectVo.getScanVersion() + CommConstants.CommonVal.ONE) : (long) CommConstants.CommonVal.ONE;

        // 标准化扫描文件路径
        scanFileUrls = normalizeScanFileUrls(scanFileUrls);

        // 按文件分组统计问题数量
        Map<String, List<ScanProjectDetail>> fileGroupMap = buildFileGroupMap(scanProjectDetailList);

        // 构建旧文件数据映射（必须在更新旧记录之前执行）
        Map<String, ScanProjectFile> oldFileDataMap = buildOldFileDataMap(fileGroupMap, project.getId());

        // 处理已修复的文件（目前只处理增量扫描）
        handleFixedFiles(isFull, scanFileUrls, fileGroupMap, project.getId(), nowScanTime, oldFileDataMap);

        // 更新旧扫描记录（必须在查询旧数据之后执行）
        updatePreviousScanRecords(isFull, scanFileUrls, project.getId());

        // 创建扫描项目文件记录
        List<ScanProjectFile> scanProjectFileList = createScanProjectFiles(fileGroupMap, oldFileDataMap, project.getId(), scanVersion);

        // 插入文件记录并获取ID映射
        Map<String, Long> fileIdMap = insertFileRecordsAndGetIds(scanProjectFileList);

        // 关联详情记录到文件
        linkDetailRecordsToFiles(scanProjectDetailList, fileIdMap, scanVersion);

        // 创建项目扫描记录
        createScanProjectRecord(project, projectVo, nowScanTime, lastCommitTime, scanVersion);
    }

    /**
     * 标准化扫描文件路径
     */
    private List<String> normalizeScanFileUrls(List<String> scanFileUrls) {
        if (CollUtil.isNotEmpty(scanFileUrls)) {
            return scanFileUrls.stream()
                .map(url -> url.replaceAll("\\\\", "/"))
                .distinct()
                .collect(Collectors.toList());
        }
        return scanFileUrls;
    }

    /**
     * 更新旧扫描记录
     */
    private void updatePreviousScanRecords(Boolean isFull, List<String> scanFileUrls, Long projectId) {
        if (isFull) {
            // 全量扫描：将该项目的所有旧记录标记为非最新
            log.info("全量扫描 - 更新项目[{}]的所有旧记录为非最新状态", projectId);
            // 传入null表示更新所有记录
            scanProjectDetailService.updatePreviousScanToOld(null, projectId);
            scanProjectService.updatePreviousScanToOld(projectId);
            scanProjectFileService.updatePreviousScanToOld(projectId, null);
        } else {
            // 增量扫描：只更新涉及的文件记录为非最新
            log.info("增量扫描 - 更新项目[{}]涉及文件的旧记录为非最新状态", projectId);
            if (CollUtil.isNotEmpty(scanFileUrls)) {
                scanProjectDetailService.updatePreviousScanToOld(scanFileUrls, projectId);
                scanProjectFileService.updatePreviousScanToOld(projectId, scanFileUrls);
            }
            scanProjectService.updatePreviousScanToOld(projectId);
        }
    }

    /**
     * 按文件分组统计问题数量
     */
    private Map<String, List<ScanProjectDetail>> buildFileGroupMap(List<ScanProjectDetail> scanProjectDetailList) {
        Map<String, List<ScanProjectDetail>> fileGroupMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(scanProjectDetailList)) {
            fileGroupMap = scanProjectDetailList.stream()
                .collect(Collectors.groupingBy(ScanProjectDetail::getScanFileUrl));
        }
        return fileGroupMap;
    }

    /**
     * 构建旧文件数据映射
     */
    private Map<String, ScanProjectFile> buildOldFileDataMap(Map<String, List<ScanProjectDetail>> fileGroupMap, Long projectId) {
        Map<String, ScanProjectFile> oldFileDataMap = new HashMap<>(fileGroupMap.size());
        for (String fileUrl : fileGroupMap.keySet()) {
            ScanProjectFile oldFileData = scanProjectFileService.selectLatestByPidAndScanFileUrl(projectId, fileUrl);
            if (oldFileData != null) {
                oldFileDataMap.put(fileUrl, oldFileData);
            }
        }
        return oldFileDataMap;
    }

    /**
     * 处理已修复的文件（目前只处理增量扫描）
     */
    private void handleFixedFiles(Boolean isFull, List<String> scanFileUrls, Map<String, List<ScanProjectDetail>> fileGroupMap, Long projectId, Date nowScanTime, Map<String, ScanProjectFile> oldFileDataMap) {
        if (!isFull && CollUtil.isNotEmpty(scanFileUrls)) {
            // 增量扫描：处理在变更文件列表中但没有问题的文件
            for (String scanFileUrl : scanFileUrls) {
                if (!fileGroupMap.containsKey(scanFileUrl)) {
                    // 这个文件在增量扫描中但没有问题，说明已经修复
                    ScanProjectFile oldFileData = oldFileDataMap.get(scanFileUrl);
                    if (oldFileData != null && CommConstants.CommonValStr.ONE.equals(oldFileData.getStatus())) {
                        // 旧数据状态为已指派未处理，现在文件已修复，更新状态为已处理
                        scanProjectFileService.updateFileStatusToHandled(oldFileData.getId(), nowScanTime);
                        log.info("增量扫描 - 文件 {} 已修复，更新状态为已处理", scanFileUrl);
                    }
                }
            }
        }
        // TODO: 全量扫描中的已修复文件处理逻辑需要优化，避免额外的数据库查询
    }

    /**
     * 创建扫描项目文件记录
     */
    private List<ScanProjectFile> createScanProjectFiles(Map<String, List<ScanProjectDetail>> fileGroupMap, Map<String, ScanProjectFile> oldFileDataMap, Long projectId, Long scanVersion) {
        List<ScanProjectFile> scanProjectFileList = new ArrayList<>(fileGroupMap.size());
        for (Map.Entry<String, List<ScanProjectDetail>> entry : fileGroupMap.entrySet()) {
            String fileUrl = entry.getKey();
            List<ScanProjectDetail> fileDetails = entry.getValue();
            // 统计该文件的严重问题和一般问题数量
            Long blockerCount = fileDetails.stream()
                .filter(detail -> detail.getScanPriority().equals((long) CommConstants.CommonVal.ONE))
                .count();
            Long criticalCount = fileDetails.stream()
                .filter(detail -> detail.getScanPriority().equals((long) CommConstants.CommonVal.TWO))
                .count();
            // 创建文件记录
            ScanProjectFile scanProjectFile = new ScanProjectFile();
            scanProjectFile.setPId(projectId);
            scanProjectFile.setScanVersion(scanVersion);
            scanProjectFile.setLastScanFlag(CommConstants.CommonVal.ONE);
            scanProjectFile.setScanFileUrl(fileUrl);
            scanProjectFile.setBlockerAmount(blockerCount);
            scanProjectFile.setCriticalAmount(criticalCount);
            // 根据旧数据的状态决定如何处理新数据
            ScanProjectFile oldFileData=oldFileDataMap.get(fileUrl);
            if (oldFileData == null || CommConstants.CommonValStr.ZERO.equals(oldFileData.getStatus()) || CommConstants.CommonValStr.TWO.equals(oldFileData.getStatus())) {
                // 情况1: 没有旧数据，或旧数据状态为未指派(0)，或旧数据状态为已处理(2)
                scanProjectFile.setStatus(CommConstants.CommonValStr.ZERO);
                log.info("文件 {} 创建新记录，状态为未指派", fileUrl);
            } else if (CommConstants.CommonValStr.ONE.equals(oldFileData.getStatus())) {
                // 情况2: 旧数据状态为已指派未处理(1)，且当前扫描仍有问题，继承旧数据的指派信息
                scanProjectFile.setStatus("1");
                scanProjectFile.setHandleUser(oldFileData.getHandleUser());
                scanProjectFile.setHandleUserId(oldFileData.getHandleUserId());
                scanProjectFile.setHandleTime(oldFileData.getHandleTime());
                scanProjectFile.setHandleRemark(oldFileData.getHandleRemark());
                scanProjectFile.setAssignUser(oldFileData.getAssignUser());
                scanProjectFile.setAssignUserId(oldFileData.getAssignUserId());
                scanProjectFile.setAssignTime(oldFileData.getAssignTime());
                log.info("文件 {} 继承旧数据的指派信息，状态为已指派未处理", fileUrl);
            }
            scanProjectFileList.add(scanProjectFile);
        }
        return scanProjectFileList;
    }

    /**
     * 插入文件记录并获取ID映射
     */
    private Map<String, Long> insertFileRecordsAndGetIds(List<ScanProjectFile> scanProjectFileList) {
        Map<String, Long> fileIdMap = new HashMap<>(scanProjectFileList.size());

        if (CollUtil.isNotEmpty(scanProjectFileList)) {
            for (ScanProjectFile file : scanProjectFileList) {
                scanProjectFileService.insert(file);
                // 插入后获取主键ID，用于关联详情记录
                fileIdMap.put(file.getScanFileUrl(), file.getId());
            }
        }

        return fileIdMap;
    }

    /**
     * 关联详情记录到文件
     */
    private void linkDetailRecordsToFiles(List<ScanProjectDetail> scanProjectDetailList, Map<String, Long> fileIdMap, Long scanVersion) {
        if (CollUtil.isNotEmpty(scanProjectDetailList)) {
            for (ScanProjectDetail detail : scanProjectDetailList) {
                Long fileId = fileIdMap.get(detail.getScanFileUrl());
                detail.setFileId(fileId);
                detail.setScanVersion(scanVersion);
            }
            // 批量插入详情记录
            scanProjectDetailService.insertBatch(scanProjectDetailList);
        }
    }

    /**
     * 创建项目扫描记录
     */
    private void createScanProjectRecord(Project project, ProjectVo projectVo, Date nowScanTime, Date lastCommitTime, Long scanVersion) {
        // 统计项目级别的问题总数
        Long blockerAmount = scanProjectDetailService.selectAmountByPriority(project.getId(), CommConstants.CommonVal.ONE);
        Long criticalAmount = scanProjectDetailService.selectAmountByPriority(project.getId(), CommConstants.CommonVal.TWO);

        // 创建项目记录
        ScanProject scanProject = new ScanProject();
        scanProject.setPId(project.getId());
        scanProject.setScanName(project.getName());
        scanProject.setScanNamespace(project.getPathWithNamespace());
        scanProject.setScanDescribe(project.getDescription());
        scanProject.setWebUrl(project.getWebUrl());
        scanProject.setLastCommitTime(lastCommitTime);
        scanProject.setDevDept(projectVo.getPDevDept());
        scanProject.setLastScanTime(nowScanTime);
        scanProject.setScanVersion(scanVersion);
        scanProject.setLastScanFlag(CommConstants.CommonVal.ONE);
        scanProject.setBlockerAmount(blockerAmount);
        scanProject.setCriticalAmount(criticalAmount);
        scanProjectService.insert(scanProject);
    }

    /**
     * 删除那些已经不存在的项目记录
     *
     * @param projectVoList 项目信息列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void delteScanProject(List<ProjectVo> projectVoList) {
        //代码扫描表处理
        log.info("开始处理代码扫描表旧数据");
        Set<Long> deleteIds = new HashSet<>();
        List<Long> pIds = projectVoList.stream().map(ProjectVo::getPId).collect(Collectors.toList());
        deleteIds.addAll(projectVoList.stream().filter(projectVo -> (projectVo.getPDesc() != null && projectVo.getPDesc().contains("废弃"))).map(ProjectVo::getPId).collect(Collectors.toList()));
        Integer pageSize = 1000;
        Integer pageNum = 1;
        Boolean hasNext = true;
        Page page;
        while (hasNext) {
            page = new Page(pageNum, pageSize);
            Page<ScanProject> pageList = scanProjectService.pageDeleteList(new ScanProjectBo(), page);
            if (CollUtil.isNotEmpty(pageList.getRecords())) {
                List<ScanProject> scanProjects = pageList.getRecords();
                scanProjects.forEach(scanProject -> {
                    Long id = scanProject.getPId();
                    if (!pIds.contains(id)) {
                        deleteIds.add(id);
                    }
                });
            }
            hasNext = page.hasNext();
            pageNum++;
        }
        if (deleteIds.size() > 0) {
            scanProjectService.batchDeleteByIds(deleteIds);
            scanProjectDetailService.batchDeleteByPids(deleteIds);
            scanProjectFileService.batchDeleteByPids(deleteIds);
        }
        Map<Long, String> devDeptInPid = new HashMap<>(projectVoList.size());
        for (ProjectVo projectVo : projectVoList) {
            devDeptInPid.put(projectVo.getPId(), projectVo.getPDevDept());
        }
        scanProjectService.updateDevDeptBatch(devDeptInPid);
    }

    /**
     * 非法xml文件处理
     *
     * @param input 输入流
     * @return
     */
    private List<String> extractFileBlocks(String input) {
        List<String> fileBlocks = new ArrayList<>();
        int start = 0;
        while (true) {
            int fileStart = input.indexOf("<file", start);
            if (fileStart == -1) {
                break;
            }
            int fileStartTagEnd = input.indexOf(">", fileStart);
            if (fileStartTagEnd == -1) {
                break;
            }
            int fileEnd = findFileEnd(input, fileStartTagEnd);
            if (fileEnd == -1) {
                break;
            }
            String fileBlock = input.substring(fileStart, fileEnd + "</file>".length());
            fileBlocks.add(fileBlock);
            start = fileEnd + "</file>".length();
        }
        return fileBlocks;
    }

    /**
     * 找出后面的</file>
     *
     * @param input    输入流
     * @param startPos <file>的结束位置
     * @return
     */
    private int findFileEnd(String input, int startPos) {
        int depth = 1;
        int index = startPos;
        while (index < input.length()) {
            int tagStart = input.indexOf("<", index);
            if (tagStart == -1) {
                break;
            }
            int tagEnd = input.indexOf(">", tagStart);
            if (tagEnd == -1) {
                break;
            }
            String tag = input.substring(tagStart, tagEnd + 1);
            if (tag.startsWith("</file>")) {
                depth--;
                if (depth == 0) {
                    return tagStart;
                }
            } else if (tag.startsWith("<file")) {
                depth++;
            }
            index = tagEnd + 1;
        }
        return -1;
    }

    /**
     * 删除40天外项目的扫描结果（软删除）
     *
     * @param projects 所有项目列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectsOutside40Days(List<Project> projects) {
        log.info("开始删除40天外项目的扫描结果");
        Set<Long> projectIdsToDelete = new HashSet<>();

        for (Project project : projects) {
            try {
                // 使用优化后的方法，直接检查是否在40天内有提交
                boolean hasCommitsWithin40Days = isProjectCommittedWithin40Days(project.getId(), branch);
                if (!hasCommitsWithin40Days) {
                    projectIdsToDelete.add(project.getId());
                    log.info("项目[{}]在40天内没有提交记录，将被软删除", project.getId());
                }
            } catch (Exception e) {
                log.error("检查项目[{}]提交时间时发生异常：", project.getId(), e);
            }
        }

        if (CollUtil.isNotEmpty(projectIdsToDelete)) {
            log.info("共找到{}个项目需要软删除", projectIdsToDelete.size());
            // 软删除扫描项目记录
            softDeleteScanProjects(projectIdsToDelete);
            // 软删除扫描项目详情记录
            softDeleteScanProjectDetails(projectIdsToDelete);
            // 软删除扫描项目文件记录
            softDeleteScanProjectFiles(projectIdsToDelete);
            log.info("完成40天外项目的软删除操作");
        } else {
            log.info("没有找到需要软删除的项目");
        }
    }

    /**
     * 软删除扫描项目记录（批量处理）
     *
     * @param projectIds 项目ID集合
     */
    private void softDeleteScanProjects(Set<Long> projectIds) {
        try {
            scanProjectService.batchSoftDeleteByProjectIds(projectIds);
            log.info("批量软删除扫描项目记录完成，共{}个项目", projectIds.size());
        } catch (Exception e) {
            log.error("批量软删除扫描项目记录时发生异常：", e);
        }
    }

    /**
     * 软删除扫描项目详情记录（批量处理）
     *
     * @param projectIds 项目ID集合
     */
    private void softDeleteScanProjectDetails(Set<Long> projectIds) {
        try {
            scanProjectDetailService.batchSoftDeleteByProjectIds(projectIds);
            log.info("批量软删除扫描项目详情记录完成，共{}个项目", projectIds.size());
        } catch (Exception e) {
            log.error("批量软删除扫描项目详情记录时发生异常：", e);
        }
    }

    /**
     * 软删除扫描项目文件记录（批量处理）
     *
     * @param projectIds 项目ID集合
     */
    private void softDeleteScanProjectFiles(Set<Long> projectIds) {
        try {
            scanProjectFileService.batchSoftDeleteByProjectIds(projectIds);
            log.info("批量软删除扫描项目文件记录完成，共{}个项目", projectIds.size());
        } catch (Exception e) {
            log.error("批量软删除扫描项目文件记录时发生异常：", e);
        }
    }

}
