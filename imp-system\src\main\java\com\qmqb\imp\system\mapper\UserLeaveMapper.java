package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.UserLeave;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 请假记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-06
 */
@DS(DataSource.GITLAB)
public interface UserLeaveMapper extends BaseMapperPlus<UserLeaveMapper, UserLeave, UserLeaveVo> {
    /**
     * 查询用户请假记录
     * @param userId
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    int countByUserIdAndTime(@Param("userId")Long userId, @Param("startDateStr")String startDateStr
        , @Param("endDateStr")String endDateStr);

    /**
     * 查询用户请假记录
     * @param userId
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    int countByUserIdInRangeTime(@Param("userId")Long userId, @Param("startDateStr")String startDateStr
        , @Param("endDateStr")String endDateStr);


    /**
     * 统计某月份请假的人员
     * @param deptId
     * @param year
     * @param month
     * @return
     */
    List<UserLeaveVo> statisticUserLeaveCount(@Param("deptId") Long deptId, @Param("year") String year, @Param("month") String month);

    /**
     * 统计某月份请假的人员
     * @param deptIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<UserLeaveVo> statisticUserLeave(@Param("deptIds") List<Long> deptIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 通过钉钉ID查询用户ID
     *
     * @param dingtalkUserId
     * @return
     */
    Long selectUserIdByDingtalk(String dingtalkUserId);

    /**
     * 批量插入请假时长数据
     *
     * @param userLeaveList
     * @return
     */
    int batchInsertOnUpdate(List<UserLeave> userLeaveList);

    /**
     * 判断假期
     *
     * @param userId
     * @param date
     * @return
     */
    boolean judgeVacation(@Param("userId") Long userId, @Param("date") Date date);

    /**
     * 获取某月某成员所有的请假数据
     * @param startDate
     * @param endDate
     * @param username
     * @param leaveType
     * @return
     */
    List<UserLeaveVo> getUserLeaveList(@Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                       @Param("username") String username, @Param("leaveType") String leaveType);
}
