package com.qmqb.imp.common.enums;

import com.qmqb.imp.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * action枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ZtTaskActionEnum {

    OPENED("opened", "打开"),

    STARTED("started", "开始"),

    PAUSED("paused", "暂停"),

    RESTARTED("restarted", "重新开始"),

    FINISHED("finished", "完成"),

    CLOSED("closed", "关闭");

    private String value;
    private String desc;

    public static String getDescByValue(String value) {
        for (ZtTaskActionEnum taskStatusEnum : ZtTaskActionEnum.values()) {
            if (StringUtils.equals(value, taskStatusEnum.getValue())) {
                return taskStatusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}
