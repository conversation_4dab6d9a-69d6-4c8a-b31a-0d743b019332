package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工作成果跟踪响应参数
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
@Builder
@Schema(description = "工作成果跟踪请求参数")
public class TrackWorkResultVO {

    @Schema(description = "所属组ID")
    private Long workGroupId;

    @Schema(description = "组别")
    private String workGroup;

    @Schema(description = "姓名")
    private String workUsername;

    @Schema(description = "查询年份")
    private Integer workYear;

    @Schema(description = "查询月份")
    private Integer workMonth;

    @Schema(description = "出勤天数")
    private String kqAttendanceDays;

    @Schema(description = "工作时长(时)")
    private String kqAttendanceWorkTime;

    @Schema(description = "工作时长(时)")
    private Integer kqRealWorkTime;

    @Schema(description = "请假时长(时)")
    private String kqLeaveTime;

    @Schema(description = "完成禅道任务")
    private Integer allWorkTaskCount;

    /**
     * 禅道任务耗时
     */
    private Double workConsumedSysTime;

    /**
     * 禅道实际时间
     */
    @Schema(description = "禅道实际时间(时)")
    private Integer workConsumedTime;

    @Schema(description = "解决bug数")
    private Integer workResolveBugCount;

    @Schema(description = "调整代码(小于1000行的新增)")
    private Integer pushCodeLines;

    @Schema(description = "创建bug数")
    private Integer createBugCount;

    @Schema(description = "关闭bug数")
    private Integer workCloseBugCount;

    @Schema(description = "完成执行用例数量")
    private Integer workCaseCount;

    @Schema(description = "文档")
    private Integer docCount;

    @Schema(description = "预警")
    private Integer warnCount;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色id")
    private String roleIds;

    @Schema(description = "耗时超过10天的任务数量")
    private Integer workOverTenDaysTaskCount;

    @Schema(description = "饱和度：工作时长/标准工作时长*100%")
    private String saturation;

    @Schema(description = " 任务效率：工作时长/完成禅道任务")
    private BigDecimal taskEfficiency;

    @Schema(description = "代码效率：调整代码行数/工作时长")
    private BigDecimal codeEfficiency;

    @Schema(description = "Bug解决率：工作时长/解决bug数")
    private BigDecimal bugSolveEfficiency;

    @Schema(description = "Bug处理率：关闭bug数/创建bug数*100%")
    private String bugHandleRate;

    @Schema(description = "执行用例效率：工作时长/执行用例")
    private BigDecimal caseEfficiency;

    @Schema(description = "迟到时长")
    private Integer kqLateMinute;

    @Schema(description = "加班时长")
    private Integer kqOvertimeApproveCount;

    public TrackWorkResultVO(){
        allWorkTaskCount = 0;
        workResolveBugCount = 0;
        pushCodeLines = 0;
        createBugCount = 0;
        workCloseBugCount = 0;
        workCaseCount = 0;
        docCount = 0;
        warnCount = 0;
        workOverTenDaysTaskCount=0;
        kqLateMinute=0;
    }

    public TrackWorkResultVO(Long workGroupId, String workGroup, String workUsername, Integer workYear, Integer workMonth,
                             String kqAttendanceWorkTime, String kqAttendanceDays, Integer kqRealWorkTime,String kqLeaveAndWorkTime, Integer allWorkTaskCount, Double workConsumedSysTime, Integer workConsumedTime,
                             Integer workResolveBugCount, Integer pushCodeLines, Integer createBugCount, Integer workCloseBugCount, Integer workCaseCount,
                             Integer docCount, Integer warnCount, String roleName,String roleIds, Integer workOverTenDaysTaskCount,
                             String saturation, BigDecimal taskEfficiency, BigDecimal codeEfficiency, BigDecimal bugSolveEfficiency, String bugHandleRate, BigDecimal caseEfficiency,Integer kqLateMinute,Integer kqOvertimeApproveCount) {
        this.workGroupId = workGroupId;
        this.workGroup = workGroup;
        this.workUsername = workUsername;
        this.workYear = workYear;
        this.workMonth = workMonth;
        this.kqAttendanceWorkTime = kqAttendanceWorkTime;
        this.kqAttendanceDays = kqAttendanceDays;
        this.kqLeaveTime = kqLeaveTime;
        this.kqRealWorkTime = kqRealWorkTime;
        this.allWorkTaskCount = allWorkTaskCount == null ? 0 : allWorkTaskCount;
        this.workConsumedSysTime = workConsumedSysTime;
        this.workConsumedTime = workConsumedTime;
        this.workResolveBugCount = workResolveBugCount == null ? 0 : workResolveBugCount;
        this.pushCodeLines = pushCodeLines == null ? 0 : pushCodeLines;
        this.createBugCount = createBugCount == null ? 0 : createBugCount;
        this.workCloseBugCount = workCloseBugCount == null ? 0 : workCloseBugCount;
        this.workCaseCount = workCaseCount == null ? 0 : workCaseCount;
        this.docCount = docCount == null ? 0 : docCount;
        this.warnCount = warnCount == null ? 0 : warnCount;
        this.roleName = roleName;
        this.roleIds = roleIds;
        this.workOverTenDaysTaskCount = workOverTenDaysTaskCount==null?0:workOverTenDaysTaskCount;
        this.saturation = saturation;
        this.taskEfficiency = taskEfficiency;
        this.codeEfficiency = codeEfficiency;
        this.bugSolveEfficiency = bugSolveEfficiency;
        this.bugHandleRate = bugHandleRate;
        this.caseEfficiency = caseEfficiency;
        this.kqLateMinute = kqLateMinute == null ? 0 : kqLateMinute;
        this.kqOvertimeApproveCount = kqOvertimeApproveCount == null ? 0 : kqOvertimeApproveCount;

    }
}
